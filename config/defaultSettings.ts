import { ProLayoutProps } from '@ant-design/pro-components';
import { toggles } from './toggle/toggle';

// microservice 暂不支持单独访问，请配合 console web 项目启动
const APP_MODE = process.env.APP_MODE || 'microservice'; // microservice 微服务版本 、 standalone 单机版

export const isMicroservice = APP_MODE === 'microservice';

/**
 * @name
 */
const defaultSettings: ProLayoutProps & {
  pwa?: boolean;
  logo?: string;
  BASE_URL?: string;
  APP_MODE: string;
  API_ADDRESS?: string; // openApi地址
  REQUEST_ADDRESS?: string; // 请求地址
  TOKEN_KEY: string;
} = {
  BASE_URL: '',
  breakpoint: 'xxl',
  navTheme: 'light',
  // 明青 Cyan-6
  colorPrimary: '#13c2c2',
  layout: 'mix',
  contentWidth: 'Fluid',
  fixedHeader: false,
  fixSiderbar: true,
  colorWeak: false,
  title: toggles.navBarTitle,
  pwa: true,
  logo: '/images/logo.png',
  iconfontUrl: '',
  token: {
    sider: {
      colorTextMenuSelected: '#13c2c2',
      colorTextMenuActive: '#13c2c2',
      colorTextMenuItemHover: '#13c2c2',
      colorMenuBackground: '#fff',
    },
    header: {
      colorTextRightActionsItem: 'rgba(0, 0, 0, 0.88)',
    },
    pageContainer: {
      paddingBlockPageContainerContent: 24,
      paddingInlinePageContainerContent: 24,
    },
  },
  menu: {
    autoClose: false,
    locale: false,
  },
  API_ADDRESS: 'http://***************:32388',
  REQUEST_ADDRESS: 'http://***************:30111',
  TOKEN_KEY: isMicroservice ? 'RKLINK_CONSOLE_TOKEN' : 'RKLINK_SQL_GUARD_TOKEN',
  APP_MODE,
};

export default defaultSettings;
