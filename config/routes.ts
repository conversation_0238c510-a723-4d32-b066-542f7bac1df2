/**
 * @name umi 的路由配置
 * @description 只支持 path,component,routes,redirect,wrappers,name,icon 的配置
 * @param path  path 只支持两种占位符配置，第一种是动态参数 :id 的形式，第二种是 * 通配符，通配符只能出现路由字符串的最后。
 * @param component 配置 location 和 path 匹配后用于渲染的 React 组件路径。可以是绝对路径，也可以是相对路径，如果是相对路径，会从 src/pages 开始找起。
 * @param routes 配置子路由，通常在需要为多个路径增加 layout 组件时使用。
 * @param redirect 配置路由跳转
 * @param wrappers 配置路由组件的包装组件，通过包装组件可以为当前的路由组件组合进更多的功能。 比如，可以用于路由级别的权限校验
 * @param name 配置路由的标题，默认读取国际化文件 menu.ts 中 menu.xxxx 的值，如配置 name 为 login，则读取 menu.ts 中 menu.login 的取值作为标题
 * @param icon 配置路由的图标，取值参考 https://ant.design/components/icon-cn， 注意去除风格后缀和大小写，如想要配置图标为 <StepBackwardOutlined /> 则取值应为 stepBackward 或 StepBackward，如想要配置图标为 <UserOutlined /> 则取值应为 user 或者 User
 * @doc https://umijs.org/docs/guides/routes
 */

//====================== 读取环境变量和加载toggle.js
//====================== 读取环境变量和加载toggle.js
const toggleEnv = process.env.TOGGLE_ENV || 'localhost';
//console.log('🔧 TOGGLE_ENV is', process.env.TOGGLE_ENV);
const path = require('path');
const togglePath = path.resolve(process.cwd(), 'config', 'toggle', toggleEnv, 'toggle.js');
//console.log('🔧 togglePath iss', togglePath);
const toggles = require(togglePath);

const dashboardComponent =
  toggles.dashboardMode === 'baseline' ? './Dashboard/BaseLineIndex' : './Dashboard/index';

let routes: any[] = [
  {
    path: '/user',
    layout: false,
    routes: [
      {
        name: 'login',
        path: '/user/login',
        component: './User/Login',
      },
    ],
  },
  {
    path: '/dashboard',
    name: '仪表盘',
    icon: 'dashboard',
    routes: [
      {
        path: '/dashboard',
        redirect: '/dashboard/index',
      },
      {
        path: '/dashboard/index',
        name: '仪表盘',
        component: dashboardComponent,
        hideInMenu: true,
      },
      {
        path: '/dashboard/sqlAuditStatus',
        name: '人工审核状态详情',
        component: './Dashboard/SqlAuditStatus',
        hideInMenu: true,
      },
      {
        path: '/dashboard/baselineAuditStatus',
        name: '基线审核状态详情',
        component: './Dashboard/BaselineAuditStatus',
        hideInMenu: true,
      },
      {
        path: '/dashboard/staticAuditStatus',
        name: '静态审核状态详情',
        component: './Dashboard/StaticAuditStatus',
        hideInMenu: true,
      },
    ],
  },
  {
    path: '/channel',
    name: '快速通道',
    icon: 'thunderbolt',
    access: 'canReadChannel',
    routes: [
      {
        path: '/channel',
        redirect: '/channel/quick-audit',
      },
      {
        path: '/channel/quick-audit',
        name: '快速审核',
        access: 'canReadChannelQuickAudit',
        component: './Channel/QuickAudit',
      },
    ],
  },
  {
    path: '/basic',
    name: '基础配置',
    access: 'canReadBasicConfig',
    icon: 'home',
    routes: [
      {
        path: '/basic',
        redirect: '/basic/audit-parameter/list',
      },

      {
        path: '/basic/rules/list',
        name: '规则管理',
        access: 'canReadRuleManagement',
        component: './Rules',
      },
      {
        path: '/basic/rules/add',
        name: '新建规则',
        component: './Rules/components/RuleDetail',
        hideInMenu: true,
        access: 'canAddRule',
      },
      {
        path: '/basic/rules/edit/:id',
        name: '编辑规则',
        component: './Rules/components/RuleDetail',
        hideInMenu: true,
        access: 'canUpdateRule',
      },
      {
        path: '/basic/rules/detail/:id',
        name: '规则详情',
        component: './Rules/components/RuleDetail',
        hideInMenu: true,
        access: 'canReadRuleManagement',
      },
      {
        path: '/basic/rules-template/list',
        name: '审核策略',
        component: './RulesTemplate',
        access: 'canReadMenuRuleTemplate',
      },
      {
        path: '/basic/rules-template/detail/:id/:dbType',
        name: '策略详情',
        component: './RulesTemplate/components/TemplateDetail',
        hideInMenu: true,
        access: 'canReadMenuRuleTemplate',
      },
      {
        path: '/basic/rules-template/add',
        name: '新增策略',
        component: './RulesTemplate/components/TemplateDetail',
        hideInMenu: true,
        access: 'canAddTemplate',
      },
      {
        path: '/basic/rules-template/edit/:id/:dbType',
        name: '编辑策略',
        component: './RulesTemplate/components/TemplateDetail',
        hideInMenu: true,
        access: 'canUpdateTemplate',
      },
    ],
  },
  {
    path: '/resource',
    name: '审核对象',
    icon: 'ungroup',
    access: 'canReadResourceManagement',
    routes: [
      {
        path: '/resource',
        redirect: '/resource/resource-group/list',
      },
      {
        path: '/resource/resource-group/list',
        name: '对象组',
        access: 'canReadResourceGroup',
        component: './ResourceGroup',
      },
      {
        path: '/resource/datasource/list',
        name: '目标对象',
        access: 'canReadDataSource',
        component: './DataSource',
      },
      {
        path: '/resource/datasource/add',
        name: '新建目标对象',
        component: './DataSource/components/DataSourceDetail',
        hideInMenu: true,
        access: 'canAddDataSource',
      },
      {
        path: '/resource/datasource/edit/:id',
        name: '编辑数据库',
        component: './DataSource/components/DataSourceDetail',
        hideInMenu: true,
        access: 'canUpdateDataSource',
      },
      {
        path: '/resource/datasource/detail/:id',
        name: '数据库详情',
        component: './DataSource/components/DataSourceDetail',
        hideInMenu: true,
        access: 'canReadDataSource',
      },
    ],
  },
  {
    path: '/autoAudit',
    name: '自动审核',
    icon: 'CoffeeOutlined',
    access: 'canReadAutomaticReview',
    routes: [
      {
        path: '/autoAudit',
        redirect: '/autoAudit/list',
      },

      {
        path: '/autoAudit/list',
        name: '任务管理',
        access: 'canReadTaskManagement',
        component: './Tasks',
      },
      {
        path: '/autoAudit/add',
        name: '新建任务',
        component: './Tasks/components/TaskForm',
        hideInMenu: true,
        access: 'canCreateTask',
      },
      {
        path: '/autoAudit/detail/:id',
        name: '任务详情',
        component: './Tasks/components/TaskDetail',
        hideInMenu: true,
        access: 'canReadTaskManagement',
      },
      {
        path: '/autoAudit/detail/:id/:id',
        name: '审核明细',
        component: './Tasks/components/ViolationRules',
        hideInMenu: true,
        access: 'canReadTaskManagement',
      },

      {
        path: '/autoAudit/report',
        name: '生成报告',
        component: './AuditOnlyReport',
        access: 'canReadOnlyReport',
      },
    ],
  },
  {
    path: '/statistics',
    name: '统计报表',
    icon: 'FundOutlined',
    access: 'canReadStatisticsReport',
    routes: [
      {
        path: '/statistics',
        redirect: '/statistics/summary',
      },
      {
        path: '/statistics/summary',
        name: '汇总统计',
        component: './StatisticsSummary',
        access: 'canReadStatisticsSummary',
      },
      {
        path: '/statistics/details',
        name: '审核结果统计',
        component: './StatisticsDetails',
        access: 'canReadStatisticsDetails',
      },
      {
        path: '/statistics/violations-rule',
        name: '违反规则统计',
        component: './StatisticsViolationsRule',
        access: 'canReadStatisticsViolationsRule',
      },
    ],
  },
  {
    path: '/system',
    name: '系统设置',
    icon: 'settingOutlined',
    access: 'canReadSystemSettings',
    routes: [
      {
        path: '/system',
        redirect: '/system/role',
      },
      {
        path: '/system/role',
        name: '角色管理',
        component: './Role',
        access: 'canReadRoleManagement',
      },
    ],
  },
  {
    name: '系统消息',
    path: '/notification',
    icon: 'BellOutlined',
    component: './Notification',
    hideInMenu: true,
  },
  {
    path: '/',
    redirect: '/dashboard',
  },
  {
    path: '*',
    layout: false,
    component: './404',
  },
];

export default routes;
