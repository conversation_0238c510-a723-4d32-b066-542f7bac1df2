server {
    listen       32327;
    listen  [::]:32327;
    server_name  102.168.100.102;
    root   /usr/share/nginx/html;
    index  index.html index.htm;
    location / {
        add_header 'Access-Control-Allow-Origin' '*' always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        add_header 'Access-Control-Allow-Headers' 'Origin, Content-Type, Accept, Authorization' always;
        if ($request_method = 'OPTIONS') {
            return 204;
        }
        try_files $uri $uri/ /index.html;
    }
    location ^~ /api {
      proxy_set_header Origin '';
      add_header Access-Control-Allow-Credentials true;
      add_header Access-Control-Allow-Headers $http_access_control_request_headers;
      add_header Access-Control-Allow-Methods POST,GET,OPTIONS,DELETE,PUT,HEAD,PATCH;
      add_header Access-Control-Allow-Origin $http_origin;
      add_header Access-Control-Expose-Headers $http_access_control_request_headers;
      if ($request_method = 'OPTIONS') {
        return 204;
      }
      if ($request_method != 'OPTIONS'){
        proxy_pass "http://microservices-gateway-service:8080";
      }
    }
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }
}
