{"name": "sql-guard", "version": "1.2.0", "description": "An out-of-box UI solution for enterprise applications", "scripts": {"analyze": "cross-env ANALYZE=1 max build", "build": "max build", "clean-cache": "rimraf node_modules/.cache", "deploy": "npm run build && npm run gh-pages", "dev": "npm run clean-cache && npm run start:dev", "gh-pages": "gh-pages -d dist", "i18n-remove": "pro i18n-remove --locale=zh-CN --write", "postinstall": "max setup", "jest": "jest", "lint": "npm run lint:js && npm run lint:prettier && npm run tsc", "lint-staged": "lint-staged", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx ", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src ", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:prettier": "prettier -c --write \"**/**.{js,jsx,tsx,ts,less,md,json}\" --end-of-line auto", "openapi": "max openapi", "prepare": "husky install", "prettier": "prettier -c --write \"**/**.{js,jsx,tsx,ts,less,md,json}\"", "preview": "npm run build && max preview --port 8000", "record": "cross-env NODE_ENV=development REACT_APP_ENV=test max record --scene=login", "serve": "umi-serve", "start": "cross-env TOGGLE_ENV=xyjr UMI_ENV=dev max dev", "start:dev": "cross-env REACT_APP_ENV=dev MOCK=none UMI_ENV=dev max dev", "start:no-mock": "cross-env MOCK=none UMI_ENV=dev max dev", "start:pre": "cross-env REACT_APP_ENV=pre UMI_ENV=dev max dev", "start:test": "cross-env REACT_APP_ENV=test MOCK=none UMI_ENV=dev max dev", "test": "jest", "test:coverage": "npm run jest -- --coverage", "test:update": "npm run jest -- -u", "tsc": "tsc --noEmit", "update-setting": "node update.js SQL审核平台"}, "lint-staged": {"**/*.{js,jsx,ts,tsx}": "npm run lint-staged:js", "**/*.{js,jsx,tsx,ts,less,md,json}": ["prettier --write"]}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "dependencies": {"@ant-design/charts": "v1", "@ant-design/icons": "^4.8.1", "@ant-design/pro-components": "^2.6.28", "@codemirror/lang-sql": "^6.8.0", "@uiw/react-codemirror": "^4.23.8", "@umijs/route-utils": "^2.2.2", "ahooks": "^3.7.10", "antd": "^5.9.4", "antd-style": "^3.5.2", "classnames": "^2.3.2", "lodash": "^4.17.21", "moment": "^2.29.4", "omit.js": "^2.0.2", "querystring": "^0.2.1", "rc-menu": "^9.12.0", "rc-util": "^5.37.0", "react": "^18.2.0", "react-dev-inspector": "^1.9.0", "react-dom": "^18.2.0", "react-helmet-async": "^1.3.0", "sql-formatter": "^15.4.10"}, "devDependencies": {"@ant-design/pro-cli": "^2.1.5", "@testing-library/react": "^13.4.0", "@types/classnames": "^2.3.1", "@types/express": "^4.17.18", "@types/history": "^4.7.11", "@types/jest": "^29.5.5", "@types/lodash": "^4.14.199", "@types/react": "^18.2.25", "@types/react-dom": "^18.2.10", "@types/react-helmet": "^6.1.7", "@umijs/fabric": "^2.14.1", "@umijs/lint": "^4.0.83", "@umijs/max": "^4.0.83", "cross-env": "^7.0.3", "eslint": "^8.50.0", "express": "^4.18.2", "gh-pages": "^3.2.3", "husky": "^7.0.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^10.5.4", "mockjs": "^1.1.0", "prettier": "^2.8.8", "swagger-ui-dist": "^4.19.1", "ts-node": "^10.9.1", "typescript": "^5.2.2", "umi-presets-pro": "^2.0.3", "umi-serve": "^1.9.11"}, "engines": {"node": ">=12.0.0"}, "create-umi": {"ignoreScript": ["docker*", "functions*", "site", "generateMock"], "ignoreDependencies": ["netlify*", "serverless"], "ignore": [".dockerignore", ".git", ".github", ".gitpod.yml", "CODE_OF_CONDUCT.md", "Dockerfile", "Dockerfile.*", "lambda", "LICENSE", "netlify.toml", "README.*.md", "azure-pipelines.yml", "docker", "CNAME", "create-umi"]}}