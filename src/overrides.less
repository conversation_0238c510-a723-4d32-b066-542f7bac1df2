.ant-layout {
  min-height: 100vh;
}
.ant-pro-sider.ant-layout-sider.ant-pro-sider-fixed {
  left: unset;
}

@media (max-width: 768px) {
  .ant-table {
    width: 100%;
    overflow-x: auto;
    &-thead > tr,
    &-tbody > tr {
      > th,
      > td {
        white-space: pre;
        > span {
          display: block;
        }
      }
    }
  }
}

// 组件样式重置
.ant-layout-content {
  min-height: calc(100vh - 120px) !important;
}
// link 按钮
.ant-btn-link {
  color: var(--ant-primary-color);
  &:not(:disabled)&:not(.ant-btn-disabled)&:hover {
    color: var(--ant-primary-color-hover);
  }
}

// modal
.ant-modal {
  .ant-modal-header {
    margin-bottom: 16px;
  }
}

// container

.ant-pro-page-container {
  .ant-page-header {
    margin-bottom: 24px;
    background-color: #fff;
  }
}
// 底部
.ant-layout-footer {
  .ant-pro-global-footer {
    margin: 24px 0;
  }
}
.ant-layout-content.ant-pro-layout-content .ant-pro-page-container.detail-container {
  min-height: calc(100vh - 190px);
  .ant-form {
    padding: 16px 24px;
    background-color: #fff;
  }
}

.ant-typography .ant-typography-copy {
  color: var(--primary-color);
  &:not(:disabled):hover {
    color: var(--primary-hover-color);
  }
}

.ant-btn-link {
  color: var(--primary-color);
  &:not(:disabled):hover {
    color: var(--primary-hover-color);
  }
}
.ant-typography a {
  color: var(--primary-color) !important;
}

.ant-picker {
  width: 100%;
}
