import '@/utils/theme';
import { RadialBar, RadialBarConfig } from '@ant-design/charts';

export type LineProps = Partial<RadialBarConfig> & {
  data: RadialBarConfig['data'];
};
const RkRadialBar: React.FC<LineProps> = ({ data, ...rest }) => {
  const config: RadialBarConfig = {
    data,
    radius: 0.8,
    innerRadius: 0.2,
    ...rest,
  };

  return <RadialBar {...config} />;
};

export default RkRadialBar;
