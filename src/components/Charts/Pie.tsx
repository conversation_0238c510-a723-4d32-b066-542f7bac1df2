import '@/utils/theme';
import { measureTextWidth, Pie, PieConfig } from '@ant-design/charts';
import { useMemo } from 'react';
export type PieProps = Partial<PieConfig> & {
  innerTitle?: string;
  innerContent?: string;
  suffix?: string;
  prefix?: string;
  showPercent?: boolean;
  showPercentValue?: boolean;
  statisticColor?: string;
  innerTitleOffsetX?: number;
  innerTitleSize?: string;
};
function renderStatistic(containerWidth: number, text: any, style: any) {
  const { width: textWidth, height: textHeight } = measureTextWidth(text, style);
  const R = containerWidth / 2; // r^2 = (w / 2)^2 + (h - offsetY)^2
  let scale = 1;
  if (containerWidth < textWidth) {
    scale = Math.min(
      Math.sqrt(Math.abs(Math.pow(R, 2) / (Math.pow(textWidth / 2, 2) + Math.pow(textHeight, 2)))),
      1,
    );
  }

  const textStyleStr = `width:${containerWidth}px;`;
  return `<div style="${textStyleStr};font-size:${scale}em;line-height:${
    scale < 1 ? 1 : 'inherit'
  };">${text}</div>`;
}
const RkPie: React.FC<PieProps> = ({
  data = [],
  innerRadius,
  innerTitle = '总数',
  innerTitleOffsetX = 0,
  innerTitleSize = '14px',
  innerContent = '',
  showPercent = false,
  showPercentValue = false,
  suffix = '',
  prefix = '',
  statisticColor = 'rgba(0,0,0,0.45)',
  ...restConfig
}) => {
  const total = useMemo(() => data?.reduce((r, d) => r + d.value, 0), [data]);
  const config: PieConfig = {
    // appendPadding: 10,
    data,
    angleField: 'value',
    colorField: 'type',
    innerRadius,
    radius: 1,

    label: {
      type: 'inner',
      offset: '-50%',
      // content: innerRadius ? ({ percent }) => `${(percent * 100).toFixed(0)}%` : '',
      // content: '',
      style: {
        fontSize: 14,
        fontWeight: 'bold',
        textAlign: 'center',
      },
    },
    meta: {
      value: {
        formatter(value) {
          if (showPercentValue) {
            return `${((value / total) * 100).toFixed(2)}%\n${value}`;
          }
          return showPercent
            ? `${((value / total) * 100).toFixed(2)}%`
            : `${prefix}${value}${suffix}`;
        },
      },
    },
    // 添加 中心统计文本 交互
    interactions: [
      {
        type: 'element-selected',
      },
      {
        type: 'element-highlight',
      },
      {
        type: 'legend-highlight',
      },
      {
        type: 'element-active',
      },
      // {
      //   type: 'pie-statistic-active',
      // },
    ],
    state: {
      // 设置 active 激活状态的样式
      active: {
        style: {
          lineWidth: 0,
          // fillOpacity: 0.65,
        },
      },
    },
    pieStyle: {
      lineWidth: 0,
    },
    // 当内半径(innerRadius) 大于 0 时才生效
    statistic: {
      title: {
        offsetX: innerTitleOffsetX,
        offsetY: -4,
        style: {
          fontSize: innerTitleSize,
          fontWeight: 400,
          color: statisticColor,
        },
        customHtml: (container, view, datum) => {
          const { width, height } = container.getBoundingClientRect();
          const d = Math.sqrt(Math.pow(width / 2, 2) + Math.pow(height / 2, 2));
          const text = datum ? datum.type : innerTitle;
          return renderStatistic(d, text, {
            fontSize: 14,
          });
        },
      },
      content: {
        offsetY: 4,
        style: {
          fontSize: '16px',
          color: statisticColor,
        },
        customHtml: (container, view, datum) => {
          const { width } = container.getBoundingClientRect();
          const text = datum ? `${datum.value}` : `${total}`;
          return renderStatistic(width, innerContent || text, {
            fontSize: 20,
          });
        },
      },
    },
    ...restConfig,
  };
  return <Pie {...config} />;
};

export default RkPie;
