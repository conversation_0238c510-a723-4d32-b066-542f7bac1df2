import { myAllRead, myAllSystemNewNotification } from '@/services/sql-guard/notification';
import { BellOutlined } from '@ant-design/icons';
import { history, useRequest } from '@umijs/max';
import { Badge, Button, Dropdown, Empty, Flex, List, MenuProps, Typography } from 'antd';
import { useMemo } from 'react';
import styles from './index.less';

const { Text } = Typography;

const empty = {
  key: 'empty',
  label: <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />,
};

const Notification = () => {
  const { data, refresh } = useRequest(myAllSystemNewNotification, {
    pollingInterval: 5000,
  });

  // 全部已读
  const { run: readAll, loading: readAllLoading } = useRequest(myAllRead, {
    onSuccess: () => {
      // 重新获取未读消息数量
      refresh();
    },
    manual: true,
  });
  const count = data?.length || 0;

  const items = useMemo(() => {
    const baseItems: MenuProps['items'] = [
      {
        key: 'title',
        label: (
          <Flex justify="space-between" align="center">
            <Text strong>消息通知{count ? `（未读 ${count}）` : ''}</Text>
            <Button type="link" onClick={readAll} loading={readAllLoading}>
              全部已读
            </Button>
          </Flex>
        ),
      },
      {
        type: 'divider',
      },
    ];
    if (count === 0) {
      baseItems.push(empty);
    } else {
      baseItems.push({
        key: 'notification',
        label: (
          <List
            size="small"
            style={{ maxHeight: 300, overflow: 'auto' }}
            itemLayout="horizontal"
            dataSource={data || []}
            renderItem={(item) => (
              <List.Item
                onClick={() => {
                  history.push('/sql/work-order/detail/' + item?.redirectInfo?.businessId);
                }}
              >
                <List.Item.Meta
                  title={
                    <Text
                      ellipsis={{
                        tooltip: true,
                      }}
                    >
                      {item.title}：{item.messageContent}
                    </Text>
                  }
                  description={item.createTime}
                />
              </List.Item>
            )}
          />
        ),
      });
    }

    return baseItems;
  }, [data]);
  return (
    <Dropdown
      menu={{
        items,
      }}
      overlayClassName={styles.content}
      placement="bottomRight"
    >
      <Badge count={count} offset={[0, 4]} size="small">
        <BellOutlined
          onClick={() => history.push('/notification')}
          style={{
            fontSize: 18,
          }}
        />
      </Badge>
    </Dropdown>
  );
};

export default Notification;
