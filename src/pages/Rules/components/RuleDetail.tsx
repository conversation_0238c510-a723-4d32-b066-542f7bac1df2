import RKCol from '@/components/RKCol';
import {
  APPLICABLE_SCENARIOS,
  APPLICABLE_SQL_TYPE,
  AUDIT_PURPOSE_TYPE,
  DATASOURCE_TYPE,
  RULE_AUDIT_MODE,
  RULE_RESOURCE,
  RULE_TARGETS_TYPE,
  RULE_TYPE,
  WARN_LEVEL,
} from '@/enums';
import WithRouteEditing, { WithRouteEditingProps } from '@/hoc/withRouteEditing';
import { addRule, detailRule, updateRule } from '@/services/sql-guard/rule';
import { onSuccessAndGoBack } from '@/utils';
import { requiredRule } from '@/utils/setting';
import {
  FooterToolbar,
  PageContainer,
  ProForm,
  ProFormDependency,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useLocation, useRequest } from '@umijs/max';
import { Row, message } from 'antd';
import { useRef } from 'react';

const baseSelectConfig: Record<string, any> = {
  transform: (value: string, namePath: string) => {
    return { [namePath]: value && Number(value) };
  },
  convertValue: (value?: number) => value?.toString(),
  fieldProps: {
    showSearch: true,
    filterOption: true,
    optionFilterProp: 'label',
  },
  rules: [requiredRule],
  placeholder: '请选择',
};

const RuleDetail: React.FC<WithRouteEditingProps> = ({ id, isEditPage }) => {
  const formRef = useRef<ProFormInstance>();
  const { pathname } = useLocation();
  const isDetailPage = pathname.includes('detail');

  const { run: add, loading: addLoading } = useRequest((value) => addRule(value), {
    manual: true,
    onSuccess: onSuccessAndGoBack,
    formatResult: (res) => res,
  });
  const { run: edit, loading: editLoading } = useRequest((value) => updateRule(value), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success('保存成功!');
    },
    formatResult: (res) => res,
  });

  return (
    <PageContainer header={{ title: false }} className="detail-container">
      <ProForm
        disabled={isDetailPage}
        formRef={formRef}
        submitter={
          isDetailPage
            ? false
            : {
                searchConfig: {
                  submitText: '保存',
                  resetText: '取消',
                },
                onReset: () => {
                  history.go(-1);
                },
                render: (props, doms) => {
                  return <FooterToolbar>{doms}</FooterToolbar>;
                },
                submitButtonProps: {
                  loading: addLoading || editLoading,
                },
              }
        }
        onFinish={async (value) => {
          const msg = isEditPage ? await edit(value) : await add(value);
          const success = msg.code === 200;
          return success;
        }}
        request={async () => {
          if (!isEditPage) return { ruleType: '99' };
          const res = await detailRule({ id });

          return res.data;
        }}
      >
        <div className="rk-none">
          <ProFormText name="id" label="id" placeholder="请输入" />
        </div>
        <Row gutter={24}>
          <RKCol>
            <ProFormSelect
              name="ruleResource"
              label="规则来源"
              options={
                isDetailPage
                  ? RULE_RESOURCE
                  : RULE_RESOURCE.filter((item) => !['1', '2', '3'].includes(item.value))
              }
              {...baseSelectConfig}
            />
          </RKCol>
          <RKCol>
            <ProFormSelect
              name="applicableScene"
              label="适用场景"
              options={APPLICABLE_SCENARIOS}
              {...baseSelectConfig}
            />
          </RKCol>
          <RKCol>
            <ProFormSelect
              name="auditMode"
              initialValue={1}
              label="审核类型"
              options={RULE_AUDIT_MODE.filter((item) => item.value === 1)}
              fieldProps={{
                showSearch: true,
                filterOption: true,
                optionFilterProp: 'label',
              }}
              rules={[requiredRule]}
              placeholder="请选择"
            />
          </RKCol>
          <RKCol>
            <ProFormSelect
              name="dbType"
              label="对象类型"
              options={DATASOURCE_TYPE?.filter((item) => Number(item.value) < 301)}
              {...baseSelectConfig}
              disabled={isEditPage}
            />
          </RKCol>
          <RKCol>
            <ProFormText
              name="ruleName"
              label="规则名称"
              placeholder="请输入"
              rules={[requiredRule]}
              disabled={isEditPage}
            />
          </RKCol>
          <RKCol>
            <ProFormSelect
              name="ruleType"
              label="规则类型"
              options={RULE_TYPE}
              {...baseSelectConfig}
              disabled={!id || isDetailPage}
            />
          </RKCol>
          <RKCol>
            <ProFormSelect
              name="alertLevel"
              label="告警等级"
              options={WARN_LEVEL}
              {...baseSelectConfig}
            />
          </RKCol>
          <RKCol>
            <ProFormSelect
              name="auditPurpose"
              label="审核目的"
              options={
                isDetailPage
                  ? AUDIT_PURPOSE_TYPE
                  : AUDIT_PURPOSE_TYPE.filter((item) => item.value !== '3')
              }
              {...baseSelectConfig}
              onChange={() => {
                formRef.current?.setFieldsValue({
                  ruleTargets: undefined,
                  applicableSqlType: undefined,
                });
              }}
            />
          </RKCol>
          <ProFormDependency name={['auditPurpose']}>
            {({ auditPurpose }) => {
              let options: Record<string, any>[] = [];
              switch (auditPurpose) {
                case 1:
                  options = [{ label: '存在_字段', value: '1' }];
                  break;
                case 2:
                  options = [{ label: '命名_字段', value: '2' }];
                  break;
                case 4:
                  options = [{ label: '禁止_值', value: '3' }];
                  break;
                default:
              }

              return (
                <RKCol>
                  <ProFormSelect
                    name="ruleTargets"
                    label="规则目标"
                    options={isDetailPage ? RULE_TARGETS_TYPE : options}
                    {...baseSelectConfig}
                    onChange={() => {
                      formRef.current?.setFieldsValue({
                        applicableSqlType: undefined,
                      });
                    }}
                  />
                </RKCol>
              );
            }}
          </ProFormDependency>
          <ProFormDependency name={['ruleTargets']}>
            {({ ruleTargets }) => {
              let options: Record<string, any>[] = [];
              switch (ruleTargets) {
                case 1:
                  options = [{ label: '创建表', value: '5' }];
                  break;
                case 2:
                  options = [{ label: '创建表', value: '5' }];
                  break;
                case 3:
                  options = [
                    { label: '插入语句', value: '2' },
                    { label: '更新语句', value: '3' },
                  ];
                  break;
                default:
              }
              return (
                <RKCol>
                  <ProFormSelect
                    name="applicableSqlType"
                    label="SQL类型"
                    options={isDetailPage ? APPLICABLE_SQL_TYPE : options}
                    {...baseSelectConfig}
                  />
                </RKCol>
              );
            }}
          </ProFormDependency>

          <RKCol>
            <ProFormTextArea
              name="description"
              label="规则描述"
              placeholder="请输入"
              fieldProps={{ autoSize: { minRows: 1, maxRows: 5 } }}
              rules={[requiredRule]}
            />
          </RKCol>
          <ProFormDependency name={['ruleResource']}>
            {({ ruleResource }) => {
              if (ruleResource !== 1) {
                return (
                  <RKCol>
                    <ProFormTextArea
                      name="ruleScript"
                      label="规则脚本"
                      placeholder="请输入"
                      fieldProps={{ autoSize: { minRows: 1, maxRows: 5 } }}
                      rules={[requiredRule]}
                    />
                  </RKCol>
                );
              }
            }}
          </ProFormDependency>
        </Row>
      </ProForm>
    </PageContainer>
  );
};

export default WithRouteEditing(RuleDetail);
