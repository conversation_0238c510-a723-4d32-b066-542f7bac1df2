import { getBaseLineDetailsListByRuleId } from '@/services/sql-guard/rule';
import { defaultTableConfig } from '@/utils/setting';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Modal } from 'antd';
import React, { useEffect } from 'react';

interface BaseLineDetailModalProps {
  open: boolean;
  onCancel: () => void;
  ruleId: string;
}

const BaseLineDetailModal: React.FC<BaseLineDetailModalProps> = ({ open, onCancel, ruleId }) => {
  const {
    data: baseLineDetails = [],
    loading,
    run,
    mutate,
  } = useRequest(() => getBaseLineDetailsListByRuleId({ ruleId }), {
    manual: true,
    formatResult: (res) => (res.code === 200 && res.data ? res.data : []),
  });

  useEffect(() => {
    if (open && ruleId) {
      mutate([]);
      run();
    }
  }, [open, ruleId]);

  const columns: ProColumns<API.RuleBaseLineDetailResponse>[] = [
    {
      title: '基线技术要求',
      dataIndex: 'baseLineRequirement',
      ellipsis: true,
      width: 300,
    },
    {
      title: '基线标准点（参数）',
      dataIndex: 'baseLineStandardPoint',
      ellipsis: true,
      width: 200,
    },
    {
      title: '说明',
      dataIndex: 'description',
      ellipsis: true,
      width: 200,
    },
  ];

  return (
    <Modal
      title="基线要求详情"
      open={open}
      onCancel={onCancel}
      width={900}
      footer={null}
      destroyOnClose
    >
      <ProTable<API.RuleBaseLineDetailResponse>
        {...defaultTableConfig}
        columns={columns}
        dataSource={baseLineDetails}
        loading={loading}
        search={false}
        options={false}
        pagination={false}
        footer={() => <div style={{ textAlign: 'right' }}>共 {baseLineDetails.length} 条</div>}
      />
    </Modal>
  );
};

export default BaseLineDetailModal;
