import RKCol from '@/components/RKCol';
import { AUDIT_STATUS, DATASOURCE_TYPE, RESULT_SOURCE, RULE_AUDIT_MODE } from '@/enums';
import { useDataSourceList } from '@/hooks/useDataSourceList';
import { useListAuthGroup } from '@/hooks/useResourceGroupList';
import { downloadReport1, previewReport } from '@/services/sql-guard/statistics';
import { option2enum } from '@/utils';
import type { ProFormInstance } from '@ant-design/pro-components';
import {
  PageContainer,
  ProForm,
  ProFormDateTimeRangePicker,
  ProFormSelect,
} from '@ant-design/pro-components';
import { Button, Row, Spin, message } from 'antd';
import type { DefaultOptionType } from 'antd/es/select';
import dayjs from 'dayjs';
import React, { useRef, useState } from 'react';

// 默认时间范围：前一周，结束日期改为今天
const defaultDateRange = [dayjs().subtract(7, 'day').startOf('day'), dayjs().endOf('day')];

const dateTimeFormat = 'YYYY-MM-DD HH:mm:ss';

const AuditOnlyReport: React.FC = () => {
  const formRef = useRef<ProFormInstance>();
  const { resourceGroupList, resourceGroupLoading } = useListAuthGroup();
  const [selectedDbType, setSelectedDbType] = useState<number>();
  const [selectedAuditMode, setSelectedAuditMode] = useState<number>();
  const { dataSourceList, dataSourceLoading, runDataSource } = useDataSourceList();
  const [htmlContent, setHtmlContent] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [exporting, setExporting] = useState(false);

  const handleSubmit = async (values: any) => {
    const { dateRange, ...rest } = values;
    const startDate = dateRange?.[0]
      ? dayjs(dateRange[0]).format(dateTimeFormat)
      : defaultDateRange[0].format(dateTimeFormat);
    const endDate = dateRange?.[1]
      ? dayjs(dateRange[1]).format(dateTimeFormat)
      : defaultDateRange[1].format(dateTimeFormat);

    setLoading(true);
    try {
      const response = await previewReport({
        ...rest,
        startDate,
        endDate,
      });
      setHtmlContent(response);
    } catch (error) {
      message.error('预览失败');
    } finally {
      setLoading(false);
    }
  };

  const handleExport = async () => {
    const values = formRef.current?.getFieldsValue();
    if (!values) {
      message.warning('请先设置筛选条件');
      return;
    }

    const { dateRange, ...rest } = values;
    const startDate = dateRange?.[0]
      ? dayjs(dateRange[0]).format(dateTimeFormat)
      : defaultDateRange[0].format(dateTimeFormat);
    const endDate = dateRange?.[1]
      ? dayjs(dateRange[1]).format(dateTimeFormat)
      : defaultDateRange[1].format(dateTimeFormat);

    setExporting(true);
    try {
      const response = await downloadReport1({
        ...rest,
        startDate,
        endDate,
      });

      const url = URL.createObjectURL(
        new Blob([response], {
          type: 'text/html',
        }),
      );
      const link = document.createElement('a');
      link.style.display = 'none';
      link.href = url;
      link.setAttribute('download', `统计报告(${startDate}到${endDate}).html`);
      document.body.appendChild(link);
      link.click();
      URL.revokeObjectURL(url);
      document.body.removeChild(link);
      message.success('导出成功');
    } catch (error) {
      message.error('导出失败，请稍后重试');
    } finally {
      setExporting(false);
    }
  };

  const getDbTypeOptions = (auditMode?: number): DefaultOptionType[] => {
    return DATASOURCE_TYPE.filter((item) => {
      const itemValue = Number(item.value);
      if (auditMode === 3) {
        return itemValue >= 301 && itemValue <= 308;
      }
      return itemValue >= 1 && itemValue <= 6;
    }).map((item) => ({
      label: item.label,
      value: item.value,
    }));
  };

  return (
    <PageContainer header={{ title: false }}>
      <ProForm
        formRef={formRef}
        layout="horizontal"
        onFinish={handleSubmit}
        onReset={() => {
          setHtmlContent('');
        }}
        submitter={{
          searchConfig: {
            submitText: '生成报告',
          },
          render: (props, dom) => [
            ...dom,
            <Button key="export" type="primary" onClick={handleExport} loading={exporting}>
              导出报告
            </Button>,
          ],
        }}
      >
        <Row gutter={24}>
          <RKCol lg={12} md={12} sm={12}>
            <ProFormDateTimeRangePicker
              name="dateRange"
              label="时间范围"
              initialValue={defaultDateRange}
              fieldProps={{
                allowClear: false,
                format: dateTimeFormat,
              }}
            />
          </RKCol>
          <RKCol>
            <ProFormSelect<string>
              name="auditMode"
              label="审核类型"
              valueEnum={option2enum(RULE_AUDIT_MODE)}
              fieldProps={{
                showSearch: true,
                filterOption: true,
                optionFilterProp: 'label',
                onChange: (value: string) => {
                  const auditMode = Number(value);
                  formRef.current?.setFieldsValue({ dbType: undefined, sourceId: undefined });
                  setSelectedAuditMode(auditMode);
                  setSelectedDbType(undefined);
                },
              }}
            />
          </RKCol>
          <RKCol>
            <ProFormSelect<string>
              name="dbType"
              label="对象类型"
              dependencies={['auditMode']}
              fieldProps={{
                disabled: !selectedAuditMode,
                showSearch: true,
                filterOption: true,
                optionFilterProp: 'label',
                options: getDbTypeOptions(selectedAuditMode),
                placeholder: '请选择对象类型',
                onChange: (value: string) => {
                  const dbType = Number(value);
                  formRef.current?.setFieldsValue({ sourceId: undefined });
                  setSelectedDbType(dbType);
                  if (selectedAuditMode) {
                    runDataSource(dbType, selectedAuditMode);
                  }
                },
              }}
            />
          </RKCol>
          <RKCol>
            <ProFormSelect<number>
              name="groupId"
              label="对象组"
              fieldProps={{
                options: resourceGroupList?.map((item) => ({
                  label: item.groupName,
                  value: item.groupId,
                })),
                loading: resourceGroupLoading,
                showSearch: true,
                placeholder: '请选择对象组',
                onChange: () => {
                  formRef.current?.setFieldsValue({ sourceId: undefined });
                },
              }}
            />
          </RKCol>
          <RKCol>
            <ProFormSelect<number>
              name="sourceId"
              label="目标对象"
              dependencies={['dbType']}
              fieldProps={{
                disabled: !selectedDbType || dataSourceLoading,
                showSearch: true,
                filterOption: true,
                optionFilterProp: 'label',
                options: dataSourceList?.map((item) => ({
                  label: item.sourceName,
                  value: item.sourceId,
                })),
                loading: dataSourceLoading,
                placeholder: '请选择目标对象',
              }}
            />
          </RKCol>
          <RKCol>
            <ProFormSelect
              name="auditStatus"
              label="审核状态"
              valueEnum={option2enum(AUDIT_STATUS)}
            />
          </RKCol>
          <RKCol>
            <ProFormSelect
              name="resultSource"
              label="结果来源"
              valueEnum={option2enum(RESULT_SOURCE)}
            />
          </RKCol>
        </Row>
      </ProForm>
      {loading ? (
        <div style={{ textAlign: 'center', marginTop: 24 }}>
          <Spin size="large" />
        </div>
      ) : htmlContent ? (
        <iframe
          className="preview-content"
          style={{
            marginTop: 24,
            padding: 24,
            background: '#fff',
            borderRadius: 4,
            boxShadow: '0 1px 2px rgba(0, 0, 0, 0.06)',
            width: '100%',
            height: '500vh',
            border: '0px solid red',
            overflow: 'auto',
            scrollbarWidth: 'none',
            msOverflowStyle: 'none',
          }}
          srcDoc={htmlContent}
        />
      ) : null}
    </PageContainer>
  );
};

export default AuditOnlyReport;
