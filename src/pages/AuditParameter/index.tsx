import { DATASOURCE_TYPE } from '@/enums';
import { listParameter } from '@/services/sql-guard/auditParameter';
import { queryPagingTable } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { Space, Tag } from 'antd';
import { useRef, useState } from 'react';
import AuditParameterModalForm from './components/AuditParameterModalForm';

const Rules: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const [modalVisit, setModalVisit] = useState<boolean>(false);
  const [initialValues, setInitialValues] = useState<API.AuditParameterResponse>({});

  const onEdit = (record: API.AuditParameterResponse) => {
    setModalVisit(true);
    setInitialValues(record);
  };

  const columns: ProColumns<API.AuditParameterResponse>[] = [
    {
      title: '参数名称',
      dataIndex: 'paramKey',
      fixed: 'left',
      width: 200,
      ellipsis: true,
      copyable: true,
      render: (text, record) => {
        return (
          <a
            key="detail"
            className="rk-a-span"
            onClick={() => {
              setModalVisit(true);
              setInitialValues({ ...record, detail: true } as API.AuditParameterResponse);
            }}
          >
            {text}
          </a>
        );
      },
    },
    {
      title: '对象类型',
      dataIndex: 'dbType',
      valueType: 'select',
      width: 100,
      ellipsis: true,
      fieldProps: {
        showSearch: true,
        filterOption: true,
        optionFilterProp: 'label',
        options: DATASOURCE_TYPE.filter((item) => {
          const itemValue = Number(item.value);
          return itemValue >= 1 && itemValue <= 6;
        }),
      },
      hideInTable: true,
      initialValue: '3',
    },
    {
      title: '数据库类型',
      dataIndex: 'dbTypeName',
      width: 170,
      ellipsis: true,
      hideInSearch: true,
      renderText: (text, record) => {
        return (
          <Tag color={DATASOURCE_TYPE.find((item) => Number(item.value) === record.dbType)?.color}>
            {text}
          </Tag>
        );
      },
    },
    {
      title: '参数值',
      dataIndex: 'paramValue',
      width: 100,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '参数值类型',
      dataIndex: 'paramValueTypeName',
      width: 100,
      ellipsis: true,
      hideInSearch: true,
    },

    {
      title: '描述',
      dataIndex: 'description',
      width: 250,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      width: 120,
      valueType: 'dateTime',
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '操作',
      width: 150,
      key: 'option',
      fixed: 'right',
      valueType: 'option',
      align: 'center',
      render: (text, record) => {
        return (
          <Space>
            <a key="edit" onClick={() => onEdit(record)}>
              编辑
            </a>
          </Space>
        );
      },
    },
  ];
  return (
    <PageContainer header={{ title: false }}>
      <ProTable<API.AuditParameterResponse>
        {...defaultTableConfig}
        actionRef={tableRef}
        scroll={{ x: '100%' }}
        search={{
          defaultCollapsed: false,
          filterType: 'query',
        }}
        columns={columns}
        headerTitle="审核参数列表"
        request={async (params) => queryPagingTable<API.listParameterParams>(params, listParameter)}
      />
      <AuditParameterModalForm
        initialValues={initialValues}
        onOpenChange={(visit) => setModalVisit(visit)}
        open={modalVisit}
        onFinish={async () => {
          tableRef.current?.reload();
        }}
      />
    </PageContainer>
  );
};

export default Rules;
