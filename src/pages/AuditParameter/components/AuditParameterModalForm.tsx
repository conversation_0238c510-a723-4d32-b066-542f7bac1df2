import { DATASOURCE_TYPE } from '@/enums';
import { updateParameter } from '@/services/sql-guard/auditParameter';
import { requiredRule } from '@/utils/setting';
import {
  ModalForm,
  ModalFormProps,
  ProForm,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { message } from 'antd';
import React, { useRef } from 'react';

const AuditParameterModalForm: React.FC<ModalFormProps> = ({
  initialValues,
  open,
  onOpenChange,
  onFinish,
}) => {
  const formRef = useRef<ProFormInstance>();
  const isEdit = initialValues?.id;
  const isDetail = initialValues?.detail;

  return (
    <ModalForm
      width="auto"
      title={isDetail ? '详情' : '编辑'}
      formRef={formRef}
      open={open}
      onOpenChange={onOpenChange}
      onFinish={async (value) => {
        const msg = await updateParameter({ ...value });
        const success = msg.code === 200;
        if (success) {
          message.success('操作成功!');
          onFinish?.(value);
        }
        return success;
      }}
      initialValues={initialValues}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        centered: true,
      }}
      disabled={isDetail}
      submitter={isDetail && false}
    >
      <div className="rk-none">
        <ProFormText name="id" label="id" placeholder="请输入" />
      </div>
      <ProForm.Group>
        <ProFormText
          width="md"
          name="paramKey"
          label="参数名称"
          placeholder="请输入"
          rules={[requiredRule]}
          disabled={isEdit}
        />
        <ProFormSelect
          width="md"
          name="dbType"
          label="数据库类型"
          options={DATASOURCE_TYPE}
          fieldProps={{
            showSearch: true,
            filterOption: true,
            optionFilterProp: 'label',
          }}
          disabled={isEdit}
          rules={[requiredRule]}
          placeholder="请选择"
          transform={(value, namePath) => {
            return { [namePath]: value && Number(value) };
          }}
          convertValue={(value) => value?.toString()}
        />
      </ProForm.Group>

      <ProForm.Group>
        <ProFormText
          width="md"
          name="paramValue"
          label="参数值"
          placeholder="请输入"
          rules={[requiredRule]}
        />
        {isDetail && (
          <ProFormText
            width="md"
            name="paramValueTypeName"
            label="参数值类型"
            placeholder="请输入"
            rules={[requiredRule]}
          />
        )}
      </ProForm.Group>
      {isDetail && (
        <ProFormText
          width="md"
          name="description"
          label="描述"
          placeholder="请输入"
          rules={[requiredRule]}
        />
      )}
    </ModalForm>
  );
};

export default AuditParameterModalForm;
