import { WORK_ORDER_STATUS } from '@/enums';
import { listWorkOrderPage, terminateWorkOrder } from '@/services/sql-guard/workorder';
import { queryPagingTable } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { history, useRequest } from '@umijs/max';
import { Button, Modal, Tag, message } from 'antd';
import { useRef } from 'react';

const WorkOrder: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();

  //中止
  const { run: stopRecord } = useRequest((value) => terminateWorkOrder(value), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success(res.message);
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });

  const columns: ProColumns<API.WorkOrderResponse>[] = [
    {
      title: '工单名称',
      dataIndex: 'workOrderName',
      fixed: 'left',
      width: 140,
      ellipsis: true,
      copyable: true,
      render: (dom, record) => {
        return (
          <a
            className="rk-a-span"
            onClick={() => history.push(`/sql/work-order/detail/${record.id}`)}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: '工单状态',
      dataIndex: 'status',
      width: 120,
      renderText: (_, record) => {
        const obj = WORK_ORDER_STATUS.find((item) => item.value === record.status);
        return <Tag color={obj?.color}>{obj?.label}</Tag>;
      },
      initialValue: '3',
    },
    {
      title: '可执行时间范围',
      dataIndex: 'executionTimeRange',
      width: 270,
    },
    {
      title: '资源组名称',
      dataIndex: 'resourceGroupName',
      width: 130,
    },
    {
      title: '数据库名称',
      dataIndex: 'dataSourceName',
      width: 130,
    },
    {
      title: '数据库实例名称',
      dataIndex: 'dbInstanceName',
      width: 130,
    },
    {
      title: '提交人',
      dataIndex: 'userName',
      width: 130,
    },
    {
      title: '执行人',
      dataIndex: 'executorUserName',
      width: 130,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      valueType: 'dateTime',
      width: 170,
    },
    {
      title: '操作',
      width: 100,
      key: 'option',
      fixed: 'right',
      valueType: 'option',
      align: 'center',
      render: (_, record) => {
        return (
          <>
            {[1, 2, 3].includes(record?.status || 0) && (
              <Button
                danger
                type="link"
                key="stop"
                onClick={() => {
                  Modal.confirm({
                    title: '确认中止',
                    content: `您确定要中止${record?.workOrderName}吗?`,
                    okText: '确认',
                    cancelText: '取消',
                    onOk: () => {
                      stopRecord({ id: record.id });
                    },
                  });
                }}
              >
                中止
              </Button>
            )}
          </>
        );
      },
    },
  ];
  return (
    <PageContainer header={{ title: false }}>
      <ProTable<API.WorkOrderResponse>
        {...defaultTableConfig}
        actionRef={tableRef}
        scroll={{ x: '100%' }}
        search={false}
        columns={columns}
        headerTitle="工单列表"
        request={async (params) => queryPagingTable(params, listWorkOrderPage)}
      />
    </PageContainer>
  );
};

export default WorkOrder;
