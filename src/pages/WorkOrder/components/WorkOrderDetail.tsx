import { WORK_ORDER_STATUS } from '@/enums';
import WithRouteEditing, { WithRouteEditingProps } from '@/hoc/withRouteEditing';
import { getApprovalFlowByResourceGroupId } from '@/services/sql-guard/approvalflow';
import {
  approveWorkOrder,
  executeWorkOrder,
  getApprovalHistoryByWorkOrderId,
  getWorkOrderDetail,
} from '@/services/sql-guard/workorder';
import { defaultTableConfig, requiredRule } from '@/utils/setting';
import { CheckCircleOutlined, CloseCircleOutlined, PlayCircleOutlined } from '@ant-design/icons';
import {
  ModalForm,
  PageContainer,
  ProCard,
  ProColumns,
  ProDescriptions,
  ProFormDateTimeRangePicker,
  ProFormTextArea,
  ProTable,
} from '@ant-design/pro-components';
import { useAccess, useModel, useRequest } from '@umijs/max';
import { Button, Space, Steps, StepsProps, Tag, Tooltip, Typography, message } from 'antd';
import dayjs from 'dayjs';
import { useMemo, useState } from 'react';
const { Text } = Typography;

const WorkOrderDetail: React.FC<WithRouteEditingProps> = ({ id }) => {
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};
  const [modalVisit, setModalVisit] = useState(false);
  const [passModalVisible, setPassModalVisible] = useState(false);
  const [initialTimeRange, setInitialTimeRange] = useState<
    [dayjs.Dayjs, dayjs.Dayjs] | undefined
  >();
  const { canApproveWorkOrder = false, canExecuteWorkOrder = false } = useAccess();

  const {
    data,
    loading: dataLoading,
    refresh,
  } = useRequest(() => getWorkOrderDetail({ workOrderId: Number(id) }), {
    ready: !!id,
    formatResult: (res) => res.data,
  });

  const {
    data: approvalData = [],
    loading: approvalLoading,
    refresh: approvalRefresh,
  } = useRequest(() => getApprovalHistoryByWorkOrderId({ workOrderId: Number(id) }), {
    ready: !!id,
    formatResult: (res) => res.data,
  });

  const {
    data: processData,
    loading: processLoading,
    refresh: processRefresh,
  } = useRequest(
    () => getApprovalFlowByResourceGroupId({ resourceGroupId: Number(data?.resourceGroupId) }),
    {
      ready: !!data?.resourceGroupId,
      formatResult: (res) => res.data,
    },
  );

  const { run: approve, loading: approveLoading } = useRequest((value) => approveWorkOrder(value), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success('审批成功!');
      setModalVisit(false);
      setPassModalVisible(false);
      refresh();
      approvalRefresh();
      processRefresh();
    },
    formatResult: (res) => res,
  });

  const { run: execute, loading: executeLoading } = useRequest(
    () => executeWorkOrder({ id: Number(id) }),
    {
      manual: true,
      onSuccess: (res) => {
        if (res.code !== 200) return;
        message.success('执行成功!');
        refresh();
      },
      formatResult: (res) => res,
    },
  );

  const customItems = useMemo(
    () =>
      approvalData?.map((item) => {
        const {
          operationName = '',
          operationInfo = '',
          operationTime = '',
          operatorName = '',
          operation = 1,
        } = item;
        return {
          title: operationName,
          description: (
            <Space direction="vertical" size={4}>
              {operatorName}
              <span style={{ fontSize: 12 }}>
                {dayjs(operationTime).format('YYYY-MM-DD HH:mm')}
              </span>
              {operationInfo && (
                <Tooltip title={operationInfo}>
                  <Text ellipsis style={{ width: '100%' }} type="secondary">
                    {operationInfo}
                  </Text>
                </Tooltip>
              )}
            </Space>
          ),
          icon:
            operation === 1 ? (
              <PlayCircleOutlined style={{ color: '#69b1ff' }} />
            ) : operation === 2 ? (
              <CheckCircleOutlined style={{ color: '#52c41a' }} />
            ) : operation === 3 ? (
              <CloseCircleOutlined style={{ color: 'red' }} />
            ) : (
              ''
            ),
        };
      }),
    [approvalData],
  );

  const columns: ProColumns<API.WorkOrderSqlDetail>[] = [
    {
      title: 'SQL种类',
      dataIndex: 'kind',
    },
    {
      title: 'SQL内容',
      dataIndex: 'sql',
    },
    {
      title: '执行结果',
      dataIndex: 'executeResult',
    },
    {
      title: '影响行数',
      dataIndex: 'affectedRows',
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
        extra: [
          data?.status === 1 &&
            currentUser?.subSysRolesIds?.includes(
              approvalData?.[approvalData?.length - 1]?.nextApprovalRoleId || '',
            ) &&
            canApproveWorkOrder && (
              <Space>
                <Button
                  type="primary"
                  key="pass"
                  loading={approveLoading}
                  onClick={() => {
                    // Parse the executionTimeRange string to set initial values
                    if (data?.executionTimeRange) {
                      const timeRangeParts = data.executionTimeRange.split(' -> ');
                      if (timeRangeParts.length === 2) {
                        const startTime = dayjs(timeRangeParts[0], 'YYYY-MM-DD HH:mm');
                        const endTime = dayjs(timeRangeParts[1], 'YYYY-MM-DD HH:mm');
                        if (startTime.isValid() && endTime.isValid()) {
                          setInitialTimeRange([startTime, endTime]);
                        }
                      }
                    }
                    setPassModalVisible(true);
                  }}
                >
                  通过
                </Button>
                <Button
                  key="reject"
                  loading={approveLoading}
                  onClick={() => {
                    setModalVisit(true);
                  }}
                >
                  拒绝
                </Button>
              </Space>
            ),
          data?.status === 2 && canExecuteWorkOrder && (
            <Button key="execute" loading={executeLoading} type="primary" onClick={execute}>
              执行
            </Button>
          ),
        ].filter(Boolean),
      }}
      className="detail-container"
      loading={dataLoading || approvalLoading || processLoading}
    >
      <ProCard direction="column" title="审批流程">
        {processData?.map((item) => item.roleName || item.roleId || '提交人')?.join(' > ')}
      </ProCard>
      <ProCard direction="column" title="审批记录">
        <Steps items={customItems as StepsProps[]} size="small" />
      </ProCard>
      <ProCard direction="column" title="基础信息" style={{ marginTop: 20 }}>
        <ProDescriptions
          column={2}
          dataSource={data}
          columns={[
            { title: '工单名称', dataIndex: 'workOrderName' },
            { title: '可执行时间范围', dataIndex: 'executionTimeRange' },
            {
              title: '工单状态',
              dataIndex: 'status',
              render: (_, entity) => {
                const obj = WORK_ORDER_STATUS.find((item) => item.value === entity.status);
                return <Tag color={obj?.color}>{obj?.label}</Tag>;
              },
            },
            {
              title: '资源组名称',
              dataIndex: 'resourceGroupName',
            },
            {
              title: '数据库名称',
              dataIndex: 'dataSourceName',
            },
            {
              title: '数据库实例名称',
              dataIndex: 'dbInstanceName',
            },
            {
              title: '提交人',
              dataIndex: 'userName',
            },
            {
              title: '执行人',
              dataIndex: 'executorUserName',
            },
            {
              title: '创建时间',
              dataIndex: 'createdAt',
              valueType: 'dateTime',
            },
            {
              title: '上次更新时间',
              dataIndex: 'updatedAt',
              valueType: 'dateTime',
            },
          ]}
          bordered
        />
      </ProCard>
      <ProCard title="SQL详情" style={{ marginTop: 20 }} bodyStyle={{ padding: 0 }}>
        <ProTable
          {...defaultTableConfig}
          rowKey="auditResultId"
          style={{ marginTop: 20 }}
          columns={columns}
          dataSource={data?.workOrderSqlDetail}
          options={false} // 隐藏刷新按钮
        />
      </ProCard>
      <ModalForm<{
        executionTimeRange?: [string, string];
        operationInfo: string;
      }>
        width={400}
        title="通过"
        modalProps={{
          destroyOnClose: true,
        }}
        open={passModalVisible}
        onOpenChange={(visible) => {
          setPassModalVisible(visible);
          if (!visible) {
            // Reset the time range when modal is closed
            setInitialTimeRange(undefined);
          }
        }}
        onFinish={async (values) => {
          const form: Partial<API.WorkOrderApprovalRequest> = {
            workOrderId: Number(id),
            operation: 2,
            operationInfo: values.operationInfo,
          };
          if (values.executionTimeRange) {
            form.executionTimeRange = `${dayjs(values.executionTimeRange[0]).format(
              'YYYY-MM-DD HH:mm',
            )} -> ${dayjs(values.executionTimeRange[1]).format('YYYY-MM-DD HH:mm')}`;
          }
          approve(form);
          return true;
        }}
      >
        <ProFormDateTimeRangePicker
          name="executionTimeRange"
          label="可执行时间范围"
          initialValue={initialTimeRange}
          fieldProps={{ format: 'YYYY-MM-DD HH:mm' }}
          placeholder={['开始时间', '结束时间']}
        />
        <ProFormTextArea
          name="operationInfo"
          label="审批意见"
          fieldProps={{ autoSize: { minRows: 3, maxRows: 6 } }}
        />
      </ModalForm>
      <ModalForm<{
        operationInfo: string;
      }>
        width={400}
        title="拒绝意见"
        modalProps={{
          destroyOnClose: true,
        }}
        open={modalVisit}
        onOpenChange={setModalVisit}
        onFinish={async (values) => {
          const form = {
            workOrderId: Number(id),
            executionTimeRange: data?.executionTimeRange,
            operation: 3,
            operationInfo: values.operationInfo,
          };
          approve(form);
        }}
      >
        <ProFormTextArea
          name="operationInfo"
          rules={[requiredRule]}
          fieldProps={{
            autoSize: {
              minRows: 4,
              maxRows: 6,
            },
            maxLength: 100,
          }}
        />
      </ModalForm>
    </PageContainer>
  );
};

export default WithRouteEditing(WorkOrderDetail);
