import RKCol from '@/components/RKCol';
import { DATASOURCE_TYPE, RULE_AUDIT_MODE } from '@/enums';
import { useRuleTemplateList } from '@/hooks/useRuleTemplateList';
import { downloadReport, quickAudit } from '@/services/sql-guard/sqlaudit';
import { requiredRule } from '@/utils/setting';
import type { ProFormInstance } from '@ant-design/pro-components';
import {
  ProForm,
  ProFormDependency,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { PageContainer } from '@ant-design/pro-layout';
import { useRequest } from '@umijs/max';
import { Button, Row, Skeleton, message } from 'antd';
import React, { useRef, useState } from 'react';

const QuickAudit: React.FC = () => {
  const formRef = useRef<ProFormInstance>();
  const [auditResult, setAuditResult] = useState<string>('');
  const { ruleTemplateList, ruleTemplateLoading, runRuleTemplate } = useRuleTemplateList();

  const { run: submitAudit, loading: submitLoading } = useRequest((values) => quickAudit(values), {
    manual: true,
    onSuccess: (res) => {
      if (typeof res === 'string') {
        setAuditResult(res);
      }
    },
    formatResult: (res) => {
      return res;
    },
  });

  const [exporting, setExporting] = useState(false);

  const handleExport = async () => {
    try {
      await formRef.current?.validateFields();
    } catch (error) {
      return;
    }

    const values = formRef.current?.getFieldsValue();
    if (!values) {
      message.warning('请先填写审核信息');
      return;
    }

    const sqlArray = values?.sql
      ?.split(';')
      ?.map((sql: string) => sql.trim())
      ?.filter((sql: string) => sql);

    setExporting(true);
    try {
      const html = await downloadReport({
        ...values,
        sql: sqlArray || undefined,
        dbType: Number(values.dbType),
      });

      // 创建一个带有HTML内容的数据URL
      const dataUrl = `data:text/html;charset=utf-8,${encodeURIComponent(html)}`;

      // 创建下载链接
      const link = document.createElement('a');
      link.href = dataUrl;
      link.download = '审核报告.html';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      message.error('导出失败，请稍后重试');
    } finally {
      setExporting(false);
    }
  };

  const getFilteredDataSourceTypes = (auditMode?: number): typeof DATASOURCE_TYPE => {
    if (!auditMode) return [];
    if (auditMode === 3) {
      return DATASOURCE_TYPE.filter((item) =>
        ['301', '302', '303', '304', '305', '306', '307', '308'].includes(item.value),
      );
    }
    return DATASOURCE_TYPE.filter((item) => ['1', '2', '3', '4', '5', '6'].includes(item.value));
  };

  return (
    <PageContainer header={{ title: false }}>
      <ProForm
        formRef={formRef}
        onReset={() => {
          setAuditResult('');
        }}
        onFinish={async (values) => {
          const sqlArray = values?.sql
            ?.split(';')
            ?.map((sql: string) => sql.trim())
            ?.filter((sql: string) => sql);
          const success = await submitAudit({
            ...values,
            sql: sqlArray || undefined,
            dbType: Number(values.dbType),
          });
          return !!success;
        }}
        submitter={{
          searchConfig: {
            submitText: '生成报告',
          },
          submitButtonProps: {
            loading: submitLoading,
          },
          render: (props, dom) => [
            ...dom,
            <Button key="export" type="primary" onClick={handleExport} loading={exporting}>
              导出报告
            </Button>,
          ],
        }}
      >
        <Row gutter={24}>
          <RKCol>
            <ProFormSelect
              name="auditMode"
              label="审核类型"
              initialValue={RULE_AUDIT_MODE[0]?.value}
              options={RULE_AUDIT_MODE}
              fieldProps={{
                showSearch: true,
                filterOption: true,
                optionFilterProp: 'label',
                onChange: () => {
                  formRef.current?.setFieldValue('dbType', undefined);
                  formRef.current?.setFieldValue('ruleTemplateId', undefined);
                },
              }}
              rules={[requiredRule]}
              placeholder="请选择"
            />
          </RKCol>
          <RKCol>
            <ProFormDependency name={['auditMode']}>
              {({ auditMode }) => (
                <ProFormSelect
                  name="dbType"
                  label="对象类型"
                  options={getFilteredDataSourceTypes(auditMode)}
                  fieldProps={{
                    showSearch: true,
                    filterOption: true,
                    optionFilterProp: 'label',
                    onChange: (val) => {
                      formRef.current?.setFieldValue('ruleTemplateId', undefined);
                      runRuleTemplate(val, auditMode);
                    },
                  }}
                  disabled={!auditMode}
                  rules={[requiredRule]}
                  placeholder="请选择"
                />
              )}
            </ProFormDependency>
          </RKCol>
          <RKCol>
            <ProFormSelect
              name="ruleTemplateId"
              label="审核策略"
              options={ruleTemplateList}
              fieldProps={{
                showSearch: true,
                filterOption: true,
                optionFilterProp: 'label',
                loading: ruleTemplateLoading,
                fieldNames: {
                  label: 'templateName',
                  value: 'id',
                },
              }}
              rules={[requiredRule]}
              placeholder="请选择"
            />
          </RKCol>
        </Row>

        <Row gutter={24}>
          <RKCol>
            <ProFormText
              name="targetObjectIp"
              label="目标IP"
              placeholder="请输入"
              rules={[requiredRule]}
            />
          </RKCol>
          <RKCol>
            <ProFormText
              name="targetObjectPort"
              label="目标端口"
              placeholder="请输入"
              rules={[requiredRule]}
            />
          </RKCol>
          <RKCol>
            <ProFormText
              name="targetObjectAccount"
              label="用户名"
              placeholder="请输入"
              rules={[requiredRule]}
            />
          </RKCol>
          <RKCol>
            <ProFormText.Password
              name="targetObjectPassword"
              label="密码"
              placeholder="请输入"
              rules={[requiredRule]}
            />
          </RKCol>
        </Row>

        <ProFormDependency name={['dbType', 'auditMode']}>
          {({ dbType, auditMode }) => (
            <Row gutter={24}>
              {dbType === '2' && (
                <RKCol>
                  <ProFormText
                    name="serviceName"
                    label="服务名称"
                    placeholder="请输入"
                    rules={[requiredRule]}
                  />
                </RKCol>
              )}
              {(dbType === '5' || dbType === '6') && (
                <>
                  <RKCol>
                    <ProFormText
                      name="tenant"
                      label="租户"
                      placeholder="请输入"
                      rules={[requiredRule]}
                    />
                  </RKCol>
                  <RKCol>
                    <ProFormText
                      name="cluster"
                      label="集群"
                      placeholder="请输入"
                      rules={[requiredRule]}
                    />
                  </RKCol>
                </>
              )}
              {(auditMode === 1 || auditMode === 2) && (
                <RKCol>
                  <ProFormText
                    name="dbNameOrSchema"
                    label="数据库"
                    placeholder="请输入"
                    rules={[requiredRule]}
                  />
                </RKCol>
              )}
            </Row>
          )}
        </ProFormDependency>

        <ProFormDependency name={['auditMode']}>
          {({ auditMode }) =>
            auditMode === 1 && (
              <Row gutter={24}>
                <RKCol lg={12} md={12} sm={12}>
                  <ProFormTextArea
                    name="sql"
                    label="SQL语句"
                    placeholder="请输入SQL语句,多条语句以分号分隔，最多支持10条语句"
                    rules={[requiredRule]}
                    fieldProps={{
                      rows: 15,
                    }}
                  />
                </RKCol>
              </Row>
            )
          }
        </ProFormDependency>
      </ProForm>

      {auditResult ? (
        <iframe
          className="preview-container"
          style={{
            marginTop: 24,
            padding: 24,
            background: '#fff',
            borderRadius: 4,
            boxShadow: '0 1px 2px rgba(0, 0, 0, 0.06)',
            width: '100%',
            height: '500vh',
            border: '0px solid red',
            overflow: 'auto',
            scrollbarWidth: 'none',
            msOverflowStyle: 'none',
          }}
          srcDoc={auditResult}
        />
      ) : submitLoading ? (
        <div style={{ marginTop: 24, padding: 24, background: '#fff' }}>
          <Skeleton active paragraph={{ rows: 4 }} />
        </div>
      ) : null}
    </PageContainer>
  );
};

export default QuickAudit;
