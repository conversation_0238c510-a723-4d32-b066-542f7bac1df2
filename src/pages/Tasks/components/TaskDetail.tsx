import WithRouteEditing, { WithRouteEditingProps } from '@/hoc/withRouteEditing';
import { detailTask, listTaskResults } from '@/services/sql-guard/taskAudit';
import { defaultTableConfig } from '@/utils/setting';
import {
  ActionType,
  PageContainer,
  ProCard,
  ProColumns,
  ProDescriptions,
  ProTable,
} from '@ant-design/pro-components';
import { history, useLocation, useRequest } from '@umijs/max';
import { Space } from 'antd';
import { useRef, useState } from 'react';
import TaskResultModalForm from './TaskResultModalForm';
const TaskDetail: React.FC<WithRouteEditingProps> = ({ id }) => {
  const location = useLocation();
  const params = new URLSearchParams(location.search);
  const initialIp = params.get('initialIp') || '';
  const taskName = id;

  const tableRef = useRef<ActionType>();
  const [modalVisit, setModalVisit] = useState<boolean>(false);
  const [initialValues, setInitialValues] = useState<API.TaskResultResponse>({});
  const [activeKey, setActiveKey] = useState<string>(`${initialIp}-${taskName}`);
  const machineInfoRef = useRef<API.MachineInfo[]>();

  const { data: detailData = {} as API.TaskResponse, loading } = useRequest(
    () => detailTask({ taskName }),
    {
      ready: !!id,
      onSuccess: (data) => {
        machineInfoRef.current = (data as API.TaskResponse).machineInfos;
      },
    },
  );

  const columns: ProColumns<API.TaskResultResponse>[] = [
    {
      title: '执行结果状态',
      dataIndex: 'taskResultStatus',
      width: 100,
      ellipsis: true,
      fixed: 'left',
      hideInSearch: true,
      valueEnum: {
        3: { text: '异常结束', status: 'Error' },
        1: { text: '进行中', status: 'Processing' },
        2: { text: '正常结束', status: 'Success' },
      },
    },
    {
      title: '执行任务时间',
      dataIndex: 'executionTime',
      width: 150,
      ellipsis: true,
      hideInSearch: true,
      valueType: 'dateTime',
    },
    {
      title: '总花费时间',
      dataIndex: 'totalTime',
      width: 100,
      ellipsis: true,
      hideInSearch: true,
      renderText: (text) => `${text} 秒`,
    },
    {
      title: '采集SQL数量',
      dataIndex: 'sqlCount',
      width: 100,
      ellipsis: true,
      hideInSearch: true,
      hideInTable:
        [2, 3].includes(detailData?.auditMode || 0) ||
        (detailData?.auditMode === 1 && [2, 4, 5, 6].includes(detailData?.sourceDbType)),
    },
    {
      title: '指针开始位置',
      dataIndex: 'fileStartPosition',
      width: 100,
      ellipsis: true,
      hideInSearch: true,
      hideInTable:
        [2, 3].includes(detailData?.auditMode || 0) ||
        (detailData?.auditMode === 1 && [2, 4, 5, 6].includes(detailData?.sourceDbType)),
    },
    {
      title: '指针结束位置',
      dataIndex: 'fileEndPosition',
      width: 100,
      ellipsis: true,
      hideInSearch: true,
      hideInTable:
        [2, 3].includes(detailData?.auditMode || 0) ||
        (detailData?.auditMode === 1 && [2, 4, 5, 6].includes(detailData?.sourceDbType)),
    },
    {
      title: '日志文件路径',
      dataIndex: 'sqlFilePath',
      width: 100,
      ellipsis: true,
      hideInSearch: true,
      hideInTable:
        [2, 3].includes(detailData?.auditMode || 0) ||
        (detailData?.auditMode === 1 && [2, 4, 5, 6].includes(detailData?.sourceDbType)),
    },
    {
      title: '错误信息',
      dataIndex: 'errorMessage',
      width: 200,
      ellipsis: true,
      hideInSearch: true,
      hideInTable:
        [2, 3].includes(detailData?.auditMode || 0) ||
        (detailData?.auditMode === 1 && [2, 4, 5, 6].includes(detailData?.sourceDbType)),
    },
    {
      title: '操作',
      width: 100,
      key: 'option',
      fixed: 'right',
      valueType: 'option',
      align: 'center',
      render: (text, record) => {
        return (
          <Space>
            {([2, 3].includes(detailData?.auditMode || 0) || record.sqlCount !== 0) && (
              <a
                key="detail"
                onClick={() => {
                  setModalVisit(true);
                  setInitialValues(record);
                }}
              >
                详细
              </a>
            )}
          </Space>
        );
      },
    },
  ];

  return (
    <PageContainer header={{ title: false }} className="detail-container" loading={loading}>
      <ProCard direction="column" title="基础信息">
        <ProDescriptions
          column={2}
          dataSource={detailData}
          columns={[
            { title: '任务名称', dataIndex: 'taskName' },
            { title: '审核类型', dataIndex: 'auditModeName' },
            {
              title: '任务状态',
              dataIndex: 'taskStatus',
              valueEnum: {
                执行中: { text: '执行中', status: 'Processing' },
                未执行: { text: '未执行', status: 'default' },
              },
            },
            {
              title: '触发状态',
              dataIndex: 'triggerStatus',
              valueEnum: {
                正常: { text: '正常', status: 'success' },
                已暂停: { text: '已暂停', status: 'error' },
              },
            },
            {
              title: '对象组',
              dataIndex: 'sourceGroupName',
              renderText: (text) => {
                return (
                  <a
                    className="rk-a-span"
                    onClick={() =>
                      history.push(
                        `/resource/resource-group/list?current=1&pageSize=10&groupName=${text}`,
                      )
                    }
                  >
                    {text}
                  </a>
                );
              },
            },
            {
              title: '目标对象',
              dataIndex: 'sourceName',
              valueType: 'select',
              // fieldProps: { options: sourceList, loading: dataSourceLoading },
              render: (dom, record: API.DataSourceResponse) => {
                return (
                  <a
                    className="rk-a-span"
                    onClick={() => history.push(`/resource/datasource/detail/${record?.sourceId}`)}
                  >
                    {dom}
                  </a>
                );
              },
            },
            {
              title: '数据库类型',
              dataIndex: 'sourceDbTypeName',
            },
            {
              title: (
                <>
                  {[2, 4, 5, 6].includes((detailData as Record<string, any>)?.sourceDbType)
                    ? 'schema'
                    : '数据库'}
                </>
              ),
              dataIndex: 'dbNameOrSchema',
            },
            { title: '首次执行时间', dataIndex: 'firsttTime', valueType: 'dateTime' },
            { title: '下一次执行时间', dataIndex: 'nextExecutionTime', valueType: 'dateTime' },
            { title: '执行次数', dataIndex: 'executionCount', valueType: 'text' },
            { title: '执行间隔', dataIndex: 'interval', renderText: (text) => `${text} 分钟` },
            { title: '任务描述', dataIndex: 'taskDescription', valueType: 'textarea' },
            { title: '执行描述', dataIndex: 'triggerDescription', valueType: 'textarea' },
            { title: '执行返回信息', dataIndex: 'message', valueType: 'textarea' },
          ]}
          bordered
        />
      </ProCard>
      {machineInfoRef.current && machineInfoRef.current.length > 0 && (
        <>
          <ProCard title="执行结果" style={{ marginTop: 20 }} bodyStyle={{ padding: 0 }}>
            <ProTable
              {...defaultTableConfig}
              actionRef={tableRef}
              // scroll={{ x: '100%' }}
              columns={columns}
              tableViewRender={
                detailData?.auditMode === 1 && [1, 3].includes(detailData?.sourceDbType)
                  ? (props, defaultDom) => {
                      const ip = activeKey?.split('-')[0];
                      const machineInfo = machineInfoRef.current?.find((it) => it.ip === ip);
                      return (
                        <>
                          <ProDescriptions
                            style={{ paddingBlock: 20 }}
                            bordered
                            dataSource={machineInfo}
                            column={3}
                            columns={[
                              {
                                title: '主机IP地址',
                                dataIndex: 'ip',
                              },
                              {
                                title: '账号',
                                dataIndex: 'account',
                              },
                              {
                                title: '密码',
                                dataIndex: 'password',
                                valueType: 'password',
                              },
                              {
                                title: '日志文件路径',
                                dataIndex: 'logDir',
                              },
                            ]}
                          />
                          {defaultDom}
                        </>
                      );
                    }
                  : undefined
              }
              params={{ machineIp: activeKey.split('-')[0] }}
              request={async ({ machineIp, current: pageNum, pageSize }) => {
                const msg = await listTaskResults({
                  taskName,
                  machineIp,
                  pageNum,
                  pageSize,
                });
                return {
                  success: true,
                  data: msg.data?.records || [],
                  total: Number(msg.data?.total) || 0,
                };
              }}
              toolbar={{
                multipleLine: true,
                menu: {
                  type: 'tab',
                  activeKey,
                  onChange: (key) => {
                    setActiveKey(key as string);
                  },
                  items: machineInfoRef.current?.map((item) => ({
                    ...item,
                    key: `${item.ip}-${taskName}`,
                    label: `${item.ip}`,
                  })),
                },
              }}
            />
          </ProCard>
        </>
      )}
      <TaskResultModalForm
        initialValues={initialValues}
        onOpenChange={(visit) => setModalVisit(visit)}
        open={modalVisit}
        detailData={detailData}
        // onFinish={async () => {
        //   tableRef.current?.reload();
        // }}
      />
    </PageContainer>
  );
};

export default WithRouteEditing(TaskDetail);
