import RKCol from '@/components/RKCol';
import { DATASOURCE_TYPE, RULE_AUDIT_MODE } from '@/enums';
import WithRouteEditing, { WithRouteEditingProps } from '@/hoc/withRouteEditing';
import { useListAuthGroup } from '@/hooks/useResourceGroupList';
import { formatData } from '@/pages/AuditSql';
import {
  listDbNameOrSchemaBysourceId,
  listDsByGroupIdAndAuditMode,
} from '@/services/sql-guard/datasource';
import { addTask, detailTask } from '@/services/sql-guard/taskAudit';
import { onSuccessAndGoBack } from '@/utils';
import { requiredRule } from '@/utils/setting';
import { IPReg } from '@/utils/validator';
import {
  FooterToolbar,
  PageContainer,
  ProCard,
  ProForm,
  ProFormDateTimePicker,
  ProFormDependency,
  ProFormDigit,
  ProFormInstance,
  ProFormList,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { history, useRequest } from '@umijs/max';
import { Row, Space, Tag, message } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useRef, useState } from 'react';

const TaskForm: React.FC<WithRouteEditingProps> = ({ id, isEditPage }) => {
  const formRef = useRef<ProFormInstance>();
  const { resourceGroupList, resourceGroupLoading } = useListAuthGroup();
  const [dbType, setDbType] = useState<number>(0);
  const [auditMode, setAuditMode] = useState<number>();
  const {
    run: runSource,
    data: sourceData = [],
    loading: sourceLoading,
  } = useRequest((value) => listDsByGroupIdAndAuditMode(value), {
    manual: true,
  });

  const { run: add, loading: addLoading } = useRequest((value) => addTask(value), {
    manual: true,
    onSuccess: onSuccessAndGoBack,
    formatResult: (res) => res,
  });
  const {
    run: runInstance,
    data: instanceData = [],
    loading: instanceLoading,
  } = useRequest((value) => listDbNameOrSchemaBysourceId(value), {
    manual: true,
  });
  const instanceOptions = instanceData?.map((item: string) => ({
    label: item,
    // value: `${item}-${getRandomId()}`,
    value: item,
  }));

  const { data = {}, loading } = useRequest(() => detailTask({ taskName: id }), {
    ready: !!id,
  });

  useEffect(() => {
    if (data) {
      formRef.current?.setFieldsValue(data);
    }
  }, [data]);

  return (
    <PageContainer header={{ title: false }} className="detail-container" loading={loading}>
      <ProForm
        formRef={formRef}
        initialValues={
          isEditPage
            ? { ...data }
            : {
                firsttTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
                machineInfos: [{ ip: '', account: '', password: '', logDir: '' }],
              }
        }
        disabled={isEditPage}
        submitter={
          isEditPage
            ? false
            : {
                searchConfig: {
                  submitText: '保存',
                  resetText: '取消',
                },
                onReset: () => {
                  history.go(-1);
                },
                render: (props, doms) => {
                  return <FooterToolbar>{doms}</FooterToolbar>;
                },
                submitButtonProps: {
                  loading: addLoading,
                },
              }
        }
        onFinish={async (value) => {
          const { machineInfos = [] } = value;
          const someIp = machineInfos?.some(
            (item: Record<string, any>, index: number) =>
              machineInfos.findIndex((it: Record<string, any>) => it.ip === item.ip) !== index,
          );
          if (someIp) {
            message.error('主机IP地址不能重复!');
            return;
          }
          const msg = await add({
            ...value,
            machineInfos: [1, 3].includes(dbType) ? machineInfos : [],
          });
          const success = msg.code === 200;
          return success;
        }}
        autoFocusFirstInput
        onValuesChange={(val, curVal) => {
          if (curVal.groupId && curVal.auditMode) {
            runSource({ groupId: Number(curVal.groupId), auditMode: curVal.auditMode });
          }
          if (val.sourceId && curVal.auditMode !== 3) {
            runInstance({ sourceId: Number(val.sourceId) });
          }
        }}
      >
        <div className="rk-none">
          <ProFormText name="id" label="id" placeholder="请输入" />
        </div>
        <Row gutter={24}>
          <RKCol lg={12} md={12}>
            <ProFormText
              name="taskName"
              label="任务名称"
              placeholder="请输入"
              rules={[requiredRule]}
            />
          </RKCol>
          <RKCol>
            <ProFormSelect
              name="groupId"
              label="对象组"
              options={resourceGroupList}
              fieldProps={{
                showSearch: true,
                filterOption: true,
                optionFilterProp: 'label',
                loading: resourceGroupLoading,
                disabled: resourceGroupLoading,
                fieldNames: {
                  label: 'groupName',
                  value: 'groupId',
                },
                onChange: () => {
                  formRef.current?.setFieldsValue({
                    sourceId: undefined,
                    dbNameOrSchema: undefined,
                  });
                },
              }}
              rules={[requiredRule]}
              placeholder="请选择"
            />
          </RKCol>
          <RKCol>
            <ProFormSelect
              name="auditMode"
              label="审核类型"
              options={RULE_AUDIT_MODE}
              fieldProps={{
                showSearch: true,
                filterOption: true,
                optionFilterProp: 'label',
                onChange: (value: number) => {
                  formRef.current?.setFieldsValue({
                    sourceId: undefined,
                  });
                  if (value) {
                    setAuditMode(value);
                  }
                },
              }}
              rules={[requiredRule]}
              placeholder="请选择"
            />
          </RKCol>
          <ProFormDependency name={['groupId']}>
            {({ groupId }) => {
              if (!groupId) return <></>;
              return (
                <>
                  <RKCol lg={12} md={12}>
                    <ProFormSelect
                      name="sourceId"
                      label="目标对象"
                      options={formatData(sourceData)}
                      fieldProps={{
                        showSearch: true,
                        filterOption: true,
                        optionFilterProp: 'label',
                        loading: sourceLoading ?? true,
                        disabled: sourceLoading,
                        optionItemRender: (item: Record<string, any>) => {
                          return (
                            <Space>
                              {item.sourceName}
                              <Tag
                                color={
                                  DATASOURCE_TYPE.find((it) => Number(it.value) === item.dbType)
                                    ?.color
                                }
                              >
                                {item.dbTypeName}
                              </Tag>
                            </Space>
                          );
                        },
                        onChange: (_, option: Record<string, any>) => {
                          setDbType(option?.dbType);
                          formRef.current?.setFieldsValue({
                            dbNameOrSchema: undefined,
                          });
                        },
                      }}
                      rules={[requiredRule]}
                      placeholder="请选择"
                      transform={(value, namePath) => {
                        return { [namePath]: Number(value) };
                      }}
                      convertValue={(value) => value?.toString()}
                    />
                  </RKCol>
                </>
              );
            }}
          </ProFormDependency>

          <ProFormDependency name={['sourceId', 'auditMode']}>
            {({ sourceId, auditMode }) => {
              if (sourceId) {
                const sourceType = formatData(sourceData).find(
                  (it) => it.sourceId === sourceId,
                )?.dbType;
                return auditMode === 3 ? null : (
                  <RKCol>
                    <ProFormSelect
                      name="dbNameOrSchema"
                      label={[2, 4, 5, 6].includes(sourceType) ? 'schema' : '数据库'}
                      options={instanceOptions}
                      fieldProps={{
                        showSearch: true,
                        filterOption: true,
                        optionFilterProp: 'label',
                        loading: instanceLoading ?? true,
                        disabled: instanceLoading,
                      }}
                      rules={[requiredRule]}
                      placeholder="请选择"
                      //   transform={(value, namePath) => {
                      //     return { [namePath]: value && value.split('-')[0] };
                      //   }}
                    />
                  </RKCol>
                );
              }
            }}
          </ProFormDependency>
          <RKCol>
            <ProFormDateTimePicker
              className="ant-picker"
              name="firsttTime"
              label="首次执行时间"
              placeholder="请选择"
              rules={[requiredRule]}
              fieldProps={{
                disabledDate: (current) => {
                  return current && current < dayjs().startOf('day');
                },
              }}
            />
          </RKCol>
          <RKCol>
            <ProFormDigit
              name="interval"
              label="执行间隔"
              placeholder="请输入"
              rules={[requiredRule]}
              fieldProps={{
                addonAfter: '分钟',
                min: 0,
                precision: 0,
              }}
            />
          </RKCol>
          <RKCol>
            <ProFormTextArea
              name="taskDescription"
              label="任务描述"
              placeholder="请输入"
              rules={[requiredRule]}
              fieldProps={{
                autoSize: { minRows: 1, maxRows: 2 },
              }}
            />
          </RKCol>
          <RKCol>
            <ProFormTextArea
              name="triggerDescription"
              label="执行描述"
              placeholder="请输入"
              rules={[requiredRule]}
              fieldProps={{
                autoSize: { minRows: 1, maxRows: 2 },
              }}
            />
          </RKCol>
        </Row>
        {auditMode === 1 && [1, 3].includes(dbType) && (
          <>
            <ProFormList
              name="machineInfos"
              // label="服务器"
              creatorButtonProps={
                isEditPage
                  ? false
                  : {
                      creatorButtonText: '添加服务器',
                    }
              }
              min={1}
              copyIconProps={
                isEditPage
                  ? false
                  : {
                      tooltipText: '复制',
                    }
              }
              deleteIconProps={
                isEditPage
                  ? false
                  : {
                      tooltipText: '删除',
                    }
              }
              // initialValue={[{ ip: '', account: '', password: '', logDir: '' }]}
              itemRender={({ listDom, action }, { index }) => {
                return (
                  <ProCard
                    bordered
                    extra={action}
                    title={`服务器${index + 1}`}
                    style={{
                      marginBlockEnd: 8,
                    }}
                  >
                    {listDom}
                  </ProCard>
                );
              }}
            >
              <Row gutter={24}>
                <RKCol>
                  <ProFormText
                    name="ip"
                    rules={[
                      requiredRule,
                      {
                        pattern: IPReg,
                        message: '主机地址必须有四段，段与段之间用点分开',
                      },
                    ]}
                    label="主机IP地址"
                    placeholder="请输入"
                  />
                </RKCol>
                <RKCol>
                  <ProFormText
                    name="account"
                    rules={[requiredRule]}
                    label="账号"
                    placeholder="请输入"
                  />
                </RKCol>
                <RKCol>
                  <ProFormText.Password
                    name="password"
                    rules={[requiredRule]}
                    label="密码"
                    placeholder="请输入"
                  />
                </RKCol>
                {dbType === 3 && (
                  <RKCol>
                    <ProFormText
                      name="logDir"
                      rules={[requiredRule]}
                      label="日志文件路径"
                      placeholder="请输入"
                    />
                  </RKCol>
                )}
              </Row>
            </ProFormList>
          </>
        )}
      </ProForm>
    </PageContainer>
  );
};

export default WithRouteEditing(TaskForm);
