import { WARN_LEVEL } from '@/enums';
import WithRouteEditing, { WithRouteEditingProps } from '@/hoc/withRouteEditing';
import { listViolationsByAuditResultId } from '@/services/sql-guard/sqlaudit';
import { option2enum } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { useRef } from 'react';
import { history } from 'umi';

import { RenderErrorMessage } from '@/components/RightContent';

const ViolationRules: React.FC<WithRouteEditingProps> = ({ id }) => {
  const tableRef = useRef<ActionType>();

  const columns: ProColumns<API.AuditParameterResponse>[] = [
    {
      title: '规则',
      dataIndex: 'violatedRuleName',
      fixed: 'left',
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '告警等级',
      dataIndex: 'alertLevel',
      width: 80,
      ellipsis: true,
      hideInSearch: true,
      valueType: 'select',
      valueEnum: option2enum(WARN_LEVEL),
    },
    {
      title: '类型',
      dataIndex: 'ruleTypeName',
      width: 100,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '适用场景',
      dataIndex: 'applicableSceneName',
      width: 100,
      ellipsis: true,
      hideInSearch: true,
    },
    ...(__ENABLE_DYNAMIC_AUDIT_ENABLED__
      ? [
          {
            title: '审核目的',
            dataIndex: 'auditPurpose',
            width: 100,
            ellipsis: true,
            hideInSearch: true,
          },
        ]
      : []),
    /** {
      title: '脚本',
      dataIndex: 'ruleResourceName',
      width: 80,
      ellipsis: true,
      hideInSearch: true,
    }, */
    {
      title: '说明',
      dataIndex: 'errorMessage',
      width: 300,
      ellipsis: true,
      hideInSearch: true,
      render: RenderErrorMessage,
    },
  ];

  return (
    <PageContainer
      header={{
        title: false,
        breadcrumb: {
          items: [
            {
              onClick: (e) => {
                e.preventDefault();
                history.back();
              },
              path: '#',
              title: '返回任务详情',
            },
          ],
        },
      }}
      className="detail-container"
    >
      <ProTable<API.AuditResultViolationsResponse>
        {...defaultTableConfig}
        actionRef={tableRef}
        scroll={{ x: '100%' }}
        columns={columns}
        headerTitle="违反规则列表"
        request={async ({ current: pageNum, pageSize }) => {
          const msg = await listViolationsByAuditResultId({
            auditResultId: id!,
            pageNum,
            pageSize,
          });
          return {
            success: true,
            data: msg.data?.records || [],
            total: Number(msg.data?.total) || 0,
          };
        }}
      />
    </PageContainer>
  );
};

export default WithRouteEditing(ViolationRules);
