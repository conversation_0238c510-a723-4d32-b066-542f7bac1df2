import { AUDIT_STATUS, RULE_AUDIT_MODE } from '@/enums';
import { listTaskResultsSql } from '@/services/sql-guard/taskAudit';
import { option2enum, queryPagingTable } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import {
  ActionType,
  ModalForm,
  ModalFormProps,
  ProColumns,
  ProFormInstance,
  ProFormText,
  ProTable,
} from '@ant-design/pro-components';
import { history } from '@umijs/max';
import { Space } from 'antd';
import React, { useRef } from 'react';

const TaskResultModalForm: React.FC<ModalFormProps & { detailData: API.TaskResponse }> = ({
  initialValues,
  open,
  onOpenChange,
  detailData,
}) => {
  const formRef = useRef<ProFormInstance>();
  const tableRef = useRef<ActionType>();
  const columns: ProColumns<API.TaskResultSqlResponse>[] = [
    {
      title: 'SQL语句',
      dataIndex: 'sqlStatement',
      fixed: 'left',
      ellipsis: true,
      hideInTable: detailData?.auditMode !== 1,
    },
    {
      title: '审核类型',
      dataIndex: 'auditMode',
      width: 100,
      valueEnum: option2enum(RULE_AUDIT_MODE),
    },
    ...(__ENABLE_DYNAMIC_AUDIT_ENABLED__
      ? [
          {
            title: '优化建议',
            dataIndex: 'optimizedSql',
            width: 150,
            ellipsis: true,
          },
        ]
      : []),
    // {
    //   title: '数据库名称',
    //   dataIndex: 'sourceName',
    //   width: 100,
    //   ellipsis: true,
    //   hideInSearch: true,
    //   render: (dom, record) => {
    //     return (
    //       <a
    //         className="rk-a-span"
    //         onClick={() => history.push(`/resource/datasource/detail/${record.sourceId}`)}
    //       >
    //         {dom}
    //       </a>
    //     );
    //   },
    // },
    {
      title: '审核结果状态',
      dataIndex: 'auditStatus',
      width: 100,
      ellipsis: true,
      hideInSearch: true,
      valueType: 'select',
      valueEnum: option2enum(AUDIT_STATUS),
    },
    {
      title: '操作',
      width: 100,
      key: 'option',
      fixed: 'right',
      valueType: 'option',
      align: 'center',
      render: (text, record) => {
        const detailId = `${initialValues?.taskName}-${initialValues?.machineIp}`;
        return (
          <Space>
            {record.auditStatus === 2 && (
              <a
                key="detail"
                onClick={() =>
                  history.push(`/autoAudit/detail/${detailId}/${record.auditResultId}`)
                }
              >
                审核明细
              </a>
            )}
          </Space>
        );
      },
    },
  ];
  return (
    <ModalForm
      width={'70vw'}
      title={false}
      formRef={formRef}
      open={open}
      onOpenChange={onOpenChange}
      // onFinish={async (value) => {
      //   const msg = await updateParameter({ ...value });
      //   const success = msg.code === 200;
      //   if (success) {
      //     message.success('操作成功!');
      //     onFinish?.(value);
      //   }
      //   return success;
      // }}
      initialValues={initialValues}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        centered: true,
        closable: false,
      }}
      submitter={false}
    >
      <div className="rk-none">
        <ProFormText name="id" label="id" placeholder="请输入" />
      </div>
      <ProTable<API.TaskResultSqlResponse>
        {...defaultTableConfig}
        // scroll={{ x: '100%' }}
        actionRef={tableRef}
        columns={columns}
        headerTitle="详细列表"
        ghost
        request={async (params) =>
          await queryPagingTable<API.listTaskResultsSqlParams>(
            { ...params, taskResultId: initialValues?.id },
            listTaskResultsSql,
          )
        }
      />
    </ModalForm>
  );
};

export default TaskResultModalForm;
