import { RULE_AUDIT_MODE } from '@/enums';
import { auditResultStatisticsDetail } from '@/services/sql-guard/statistics';
import { option2enum } from '@/utils';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { useState } from 'react';

import { RenderErrorMessage } from '@/components/RightContent';

interface ExpandedRowTableProps {
  auditResultId: string;
  parentParams: {
    dbType?: number;
    auditMode?: number;
    auditStatus?: number;
    resultSource?: number;
    sqlKind?: string;
    groupId?: number;
    sourceId?: number;
    startDate: string;
    endDate: string;
  };
}

const ExpandedRowTable: React.FC<ExpandedRowTableProps> = ({ auditResultId, parentParams }) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [currPageSize, setCurrPageSize] = useState(10);

  const columns: ProColumns<API.AuditRusultStatisticsDetailResponse>[] = [
    {
      title: '序号',
      dataIndex: 'index',
      width: 80,
      render: (_, record, index) => {
        return (currentPage - 1) * currPageSize + index + 1;
      },
    },
    {
      title: '规则名称',
      dataIndex: 'violatedRuleName',
      width: 150,
      ellipsis: true,
    },
    {
      title: '审核模式',
      dataIndex: 'auditMode',
      valueEnum: option2enum(RULE_AUDIT_MODE),
      width: 50,
    },
    {
      title: '错误描述',
      dataIndex: 'errorMessage',
      width: 300,
      ellipsis: true,
      render: RenderErrorMessage,
    },
  ];

  return (
    <div style={{ padding: '0 20px 20px 20px' }}>
      <ProTable<API.AuditRusultStatisticsDetailResponse>
        headerTitle="审核结果统计明细"
        rowKey={(record) => record.ruleId || ''}
        search={false}
        options={false}
        pagination={{
          defaultPageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
        }}
        columns={columns}
        request={async (params) => {
          const { current, pageSize } = params;
          setCurrentPage(current as number);
          setCurrPageSize(pageSize as number);

          const result = await auditResultStatisticsDetail({
            ...parentParams,
            auditResultId,
            pageNum: current,
            pageSize,
          });

          return {
            data: result.data?.records || [],
            total: Number(result.data?.total) || 0,
            success: result.code === 200,
          };
        }}
      />
    </div>
  );
};

export default ExpandedRowTable;
