import { AUDIT_STATUS, DATASOURCE_TYPE, RESULT_SOURCE, RULE_AUDIT_MODE } from '@/enums';
import { useDataSourceList } from '@/hooks/useDataSourceList';
import { useListAuthGroup } from '@/hooks/useResourceGroupList';
import { detailStatistics } from '@/services/sql-guard/statistics';
import { option2enum } from '@/utils';
import { PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { request } from '@umijs/max';
import { Button, FormInstance, message } from 'antd';
import dayjs from 'dayjs';
import { useRef, useState } from 'react';
import ExpandedRowTable from './components/ExpandedRowTable';

// 默认时间范围：前一周，结束日期改为今天
const defaultDateRange = [dayjs().subtract(7, 'day').startOf('day'), dayjs().endOf('day')];

const dateTimeFormat = 'YYYY-MM-DD HH:mm:ss';

const StatisticsDetails: React.FC = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [currPageSize, setCurrPageSize] = useState(10);
  const formRef = useRef<FormInstance>(); // 新增form引用
  const { resourceGroupList, resourceGroupLoading } = useListAuthGroup();
  const [selectedDbType, setSelectedDbType] = useState<number>();
  // 根据selectedDbType动态获取数据源列表
  const { dataSourceList, dataSourceLoading, runDataSource } = useDataSourceList();

  const columns: ProColumns<API.AuditRusultStatisticsResponse>[] = [
    {
      title: '时间范围',
      dataIndex: 'dateRange',
      valueType: 'dateRange',
      colSize: 2,
      hideInTable: true,
      initialValue: defaultDateRange,
      fieldProps: {
        showTime: { format: dateTimeFormat },
        format: dateTimeFormat,
      },
    },
    {
      title: '序号',
      dataIndex: 'index',
      width: 80,
      hideInSearch: true,
      render: (_, record, index) => {
        return (currentPage - 1) * currPageSize + index + 1;
      },
    },
    {
      title: '对象组',
      dataIndex: 'groupName',
      width: 100,
      hideInSearch: true,
    },
    {
      title: '对象组',
      dataIndex: 'groupId',
      valueType: 'select',
      fieldProps: {
        options: resourceGroupList,
        loading: resourceGroupLoading,
        showSearch: true,
        fieldNames: {
          label: 'groupName',
          value: 'groupId',
        },
      },
      hideInTable: true,
    },
    {
      title: '审核类型',
      dataIndex: 'auditMode',
      width: 100,
      valueType: 'select',
      valueEnum: option2enum(RULE_AUDIT_MODE),
      fieldProps: (form) => ({
        showSearch: true,
        filterOption: true,
        optionFilterProp: 'label',
        onChange: () => {
          form?.setFieldValue('dbType', undefined);
          form?.setFieldValue('sourceId', undefined);
          setSelectedDbType(undefined);
        },
      }),
      render: (_, record) => {
        const item = RULE_AUDIT_MODE.find((i) => i.value === record.auditMode);
        return item?.label;
      },
    },
    {
      title: '对象类型',
      width: 130,
      dataIndex: 'dbType',
      valueType: 'select',
      dependencies: ['auditMode'],
      fieldProps: (form) => {
        const auditMode = Number(form?.getFieldValue?.('auditMode'));
        let filteredOptions: typeof DATASOURCE_TYPE = [];

        if (auditMode === 3) {
          filteredOptions = DATASOURCE_TYPE.filter((item) => {
            const itemValue = Number(item.value);
            return itemValue >= 301 && itemValue <= 308;
          });
        } else {
          filteredOptions = DATASOURCE_TYPE.filter((item) => {
            const itemValue = Number(item.value);
            return itemValue >= 1 && itemValue <= 6;
          });
        }
        return {
          disabled: !auditMode,
          showSearch: true,
          filterOption: true,
          optionFilterProp: 'label',
          options: filteredOptions,
          placeholder: '请选择对象类型',
          onChange: (value: number) => {
            form?.setFieldValue('sourceId', undefined);
            setSelectedDbType(value); // 当选择改变时更新dbType
            runDataSource(value, auditMode);
          },
        };
      },
      render: (_, record) => {
        const item = DATASOURCE_TYPE.find((i) => i.value === record.dbType?.toString());
        return item?.label;
      },
    },
    {
      title: '目标对象',
      dataIndex: 'sourceName',
      width: 150,
      hideInSearch: true,
    },
    {
      title: '目标对象',
      dataIndex: 'sourceId',
      width: 150,
      hideInTable: true,
      valueType: 'select',
      dependencies: ['dbType'],
      fieldProps: {
        disabled: !selectedDbType || dataSourceLoading,
        showSearch: true,
        filterOption: true,
        optionFilterProp: 'label',
        options: dataSourceList?.map((item) => ({
          label: item.sourceName,
          value: item.sourceId,
        })),
        loading: dataSourceLoading,
        placeholder: '请选择目标对象',
      },
    },
    {
      title: '审核状态',
      width: 100,
      dataIndex: 'auditStatus',
      valueType: 'select',
      fieldProps: {
        options: AUDIT_STATUS.map((opt) => ({ value: opt.value, label: opt.label })),
      },
      render: (_, record) => {
        const item = AUDIT_STATUS.find((i) => i.value === record.auditStatus?.toString());
        return item?.label;
      },
    },
    // {
    //   title: '详情信息',
    //   width: 300,
    //   dataIndex: 'detailMessages',
    //   ellipsis: true,
    //   hideInSearch: true,
    // },
    {
      title: '违反规则数量',
      width: 140,
      dataIndex: 'violationsRuleCount',
      hideInSearch: true,
    },
    ...(__ENABLE_DYNAMIC_AUDIT_ENABLED__
      ? [
          {
            title: 'SQL种类',
            dataIndex: 'sqlKind',
            width: 120,
          },
        ]
      : []),
    ...(__ENABLE_DYNAMIC_AUDIT_ENABLED__
      ? [
          {
            title: 'SQL语句',
            width: 300,
            dataIndex: 'sqlStatement',
            ellipsis: true,
            hideInSearch: true,
          },
        ]
      : []),
    ...(__ENABLE_DYNAMIC_AUDIT_ENABLED__
      ? [
          {
            title: '优化建议',
            width: 250,
            dataIndex: 'optimizedSql',
            ellipsis: true,
            hideInSearch: true,
          },
        ]
      : []),
    {
      title: '结果来源',
      width: 150,
      dataIndex: 'resultSource',
      valueType: 'select',
      fieldProps: {
        options: RESULT_SOURCE.map((opt) => ({ value: opt.value, label: opt.label })),
      },
      render: (_, record) => {
        const item = RESULT_SOURCE.find((i) => i.value === record.resultSource);
        return item?.label;
      },
    },
    {
      title: '审核时间',
      width: 180,
      dataIndex: 'createdAt',
      valueType: 'dateTime',
      hideInSearch: true,
    },
  ];

  const handleExport = async () => {
    const filters = formRef.current?.getFieldsValue();
    const { dateRange, ...rest } = filters || {};
    const startDate = dateRange?.[0]
      ? dayjs(dateRange[0]).format(dateTimeFormat)
      : defaultDateRange[0].format(dateTimeFormat);
    const endDate = dateRange?.[1]
      ? dayjs(dateRange[1]).format(dateTimeFormat)
      : defaultDateRange[1].format(dateTimeFormat);

    const result = await detailStatistics({
      ...rest,
      pageNum: 1,
      pageSize: 1,
      startDate,
      endDate,
    });
    if (!result?.data?.records?.length) {
      message.error('没有数据可以导出');
      return;
    }

    request('/api/v1/sqlaudit/statistics/auditResultStatistics/export', {
      method: 'get',
      params: {
        ...rest,
        startDate,
        endDate,
      },
      responseType: 'blob',
      getResponse: true,
      skipErrorHandler: true,
    })
      .then((response) => {
        const url = URL.createObjectURL(
          new Blob([response.data], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          }),
        );
        const link = document.createElement('a');
        link.style.display = 'none';
        link.href = url;
        link.setAttribute('download', `人工审核明细统计(${startDate}到${endDate})`); // 指定下载的文件名和类型
        document.body.appendChild(link);
        link.click();
      })
      .catch(() => {
        message.error('导出失败');
      });
  };

  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.AuditRusultStatisticsResponse>
        formRef={formRef} // 关联ProTable搜索表单
        scroll={{ x: '100%' }}
        rowKey="id"
        columns={columns}
        toolBarRender={() => [
          <Button key="export" type="primary" onClick={handleExport}>
            导出
          </Button>,
        ]}
        expandable={{
          expandedRowRender: (record) => {
            const filters = formRef.current?.getFieldsValue();
            const { dateRange, ...rest } = filters || {};
            const startDate = dateRange?.[0]
              ? dayjs(dateRange[0]).format(dateTimeFormat)
              : defaultDateRange[0].format(dateTimeFormat);
            const endDate = dateRange?.[1]
              ? dayjs(dateRange[1]).format(dateTimeFormat)
              : defaultDateRange[1].format(dateTimeFormat);

            return (
              <ExpandedRowTable
                auditResultId={record.auditResultId || ''}
                parentParams={{
                  ...rest,
                  startDate,
                  endDate,
                }}
              />
            );
          },
        }}
        search={{
          labelWidth: 100,
          defaultCollapsed: false,
          filterType: 'query',
        }}
        request={async (params) => {
          const { current, pageSize, dateRange, ...rest } = params;
          setCurrentPage(current as number);
          setCurrPageSize(pageSize as number);
          const startDate = dateRange?.[0]
            ? dayjs(dateRange[0]).format(dateTimeFormat)
            : defaultDateRange[0].format(dateTimeFormat);
          const endDate = dateRange?.[1]
            ? dayjs(dateRange[1]).format(dateTimeFormat)
            : defaultDateRange[1].format(dateTimeFormat);
          const result = await detailStatistics({
            ...rest,
            pageNum: current,
            pageSize,
            startDate,
            endDate,
          });
          return {
            data: result.data?.records?.map((record, index) => ({
              // 生成唯一key：当前页码与索引组合
              ...record,
              id: `${current}-${index}`,
            })),
            total: Number(result.data?.total) || 0,
            success: result.code === 200,
          };
        }}
        pagination={{
          defaultPageSize: 10, // 添加默认每页10条
          showSizeChanger: true,
          showQuickJumper: true,
        }}
      />
    </PageContainer>
  );
};

export default StatisticsDetails;
