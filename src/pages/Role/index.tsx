import { useAllUserList } from '@/hooks/useAllUserList';
import { usePermissionList } from '@/hooks/usePermissionList';
import { getPermissionByRoleId } from '@/services/sql-guard/permission'; // 新增：导入接口
import {
  addSubsysRole,
  assignPermissionToRoles,
  assignUsersToRoles,
  deleteSubsysRole,
  listRole,
  updateSubsysRole,
} from '@/services/sql-guard/role';
import { listUserOfRole } from '@/services/sql-guard/user'; // 新增：导入接口
import { queryPagingTable } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { Access, useAccess, useRequest } from '@umijs/max';
import { Button, Drawer, Form, Input, Modal, Select, Space, Tree, message } from 'antd';
import { DataNode } from 'antd/es/tree';
import { useRef, useState } from 'react';

const ResourceGroup: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  // 新增分配用户状态和当前对象组
  const [assignModalVisible, setAssignModalVisible] = useState<boolean>(false);
  const [selectedRole, setSelectedRole] = useState<API.RoleResponse>({});
  const [assignForm] = Form.useForm();
  const {
    canAddRole = false,
    canDeleteRole = false,
    canUpdateRole = false,
    canAssignUserToRole = false,
    canAssignPermissionToRole = false,
  } = useAccess();

  const { userList, userListLoading } = useAllUserList();
  const { permissionList } = usePermissionList();

  // 新增：搜索框状态及动态构造权限树数据
  const [searchPermission, setSearchPermission] = useState<string>('');
  const filteredPermissionTreeData = [
    {
      title: '菜单权限',
      key: 'menu',
      children: permissionList
        .filter((item) => item.type === 1 && item.description?.includes(searchPermission))
        .map((item) => ({ title: item.description, key: item.id })),
    },
    {
      title: '按钮权限',
      key: 'button',
      children: permissionList
        .filter((item) => item.type === 2 && item.description?.includes(searchPermission))
        .map((item) => ({ title: item.description, key: item.id })),
    },
    {
      title: '操作权限',
      key: 'operation',
      children: permissionList
        .filter((item) => item.type === 3 && item.description?.includes(searchPermission))
        .map((item) => ({ title: item.description, key: item.id })),
    },
  ];

  const { run: checkListOfRole, loading } = useRequest(
    (record: API.RoleResponse) => listUserOfRole({ roleId: record.id! }),
    {
      manual: true,
      onSuccess: (res) => {
        if (res.code !== 200) return;
        if (res.data) {
          setAssignModalVisible(true);
          assignForm.resetFields();
          const assignedIds = res.data.map((user: API.UserVO) => user.id);
          assignForm.setFieldsValue({ userIds: assignedIds });
        }
      },
      formatResult: (res) => res,
    },
  );
  const { run: handleAssign, loading: assignLoading } = useRequest(
    (values) =>
      assignUsersToRoles({
        roleId: selectedRole.id!,
        userIds: values.userIds,
      }),
    {
      manual: true,
      onSuccess: (res) => {
        if (res.code !== 200) return;
        message.success('分配成功');
        setAssignModalVisible(false);
        tableRef.current?.reload();
      },
      formatResult: (res) => res,
    },
  );
  const onAssign = async (record: API.RoleResponse) => {
    setSelectedRole(record);
    checkListOfRole(record);
  };
  // 提交分配用户表单
  const handleAssignSubmit = async () => {
    try {
      const values = await assignForm.validateFields();
      handleAssign(values);
    } catch (error) {}
  };

  const [roleModalVisible, setRoleModalVisible] = useState<boolean>(false);
  const [currentRole, setCurrentRole] = useState<API.RoleResponse>({});
  const [isEditMode, setIsEditMode] = useState<boolean>(false);
  const [roleForm] = Form.useForm();

  const [deleteLoading, setDeleteLoading] = useState(false);
  const [roleLoading, setRoleLoading] = useState(false);

  const handleDelete = async (id?: string) => {
    if (!id) return;
    try {
      setDeleteLoading(true);
      const res = await deleteSubsysRole({ id });
      if (res.code !== 200) return;
      message.success('删除成功');
      tableRef.current?.reload();
    } catch (error) {
      console.error('删除角色失败:', error);
    } finally {
      setDeleteLoading(false);
    }
  };

  const handleRoleSubmit = async () => {
    try {
      setRoleLoading(true);
      const values = await roleForm.validateFields();
      if (isEditMode) {
        const res = await updateSubsysRole({ ...values, id: currentRole.id });
        if (res.code !== 200) return;
        message.success('更新成功');
      } else {
        const res = await addSubsysRole(values);
        if (res.code !== 200) return;
        message.success('添加成功');
      }
      setRoleModalVisible(false);
      roleForm.resetFields();
      tableRef.current?.reload();
    } catch (error) {
      console.error(isEditMode ? '更新角色失败:' : '添加角色失败:', error);
    } finally {
      setRoleLoading(false);
    }
  };

  // 新增：权限分配状态和变量
  const [assignPermissionModalVisible, setAssignPermissionModalVisible] = useState<boolean>(false);
  const [currentRoleForPermission, setCurrentRoleForPermission] = useState<API.RoleResponse>({});
  const [permissionTreeCheckedKeys, setPermissionTreeCheckedKeys] = useState<string[]>([]);

  // 新增：权限分配请求
  const { run: assignPermissionReq, loading: assignPermissionLoading } = useRequest(
    () =>
      assignPermissionToRoles({
        roleId: currentRoleForPermission.id!,
        permissionIds: permissionTreeCheckedKeys.filter(
          (i) => !['menu', 'button', 'operation'].includes(i),
        ) as string[],
      }),
    {
      manual: true,
      onSuccess: (res) => {
        if (res.code !== 200) return;
        message.success('权限分配成功');
        setAssignPermissionModalVisible(false);
      },
      formatResult: (res) => res,
    },
  );

  const columns: ProColumns<API.RoleResponse>[] = [
    {
      title: '角色名称',
      dataIndex: 'name',
      fixed: 'left',
      width: 150,
      ellipsis: true,
      copyable: true,
    },
    {
      title: '角色描述',
      dataIndex: 'description',
      width: 200,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      width: 120,
      valueType: 'dateTime',
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '操作',
      width: 120,
      key: 'option',
      fixed: 'right',
      align: 'center',
      hideInSearch: true,
      render: (_, record) => {
        return (
          <Space>
            <Access key="assign" accessible={canAssignUserToRole}>
              <Button
                type="link"
                className="inner-table-link"
                onClick={() => onAssign(record)}
                loading={loading}
              >
                分配用户
              </Button>
            </Access>
            <Access key="update" accessible={canUpdateRole}>
              <Button
                type="link"
                className="inner-table-link"
                onClick={() => {
                  setCurrentRole(record);
                  roleForm.setFieldsValue(record);
                  setIsEditMode(true);
                  setRoleModalVisible(true);
                }}
              >
                更新
              </Button>
            </Access>
            <Access key="delete" accessible={canDeleteRole}>
              <Button
                type="link"
                className="inner-table-link"
                onClick={() => handleDelete(record.id)}
                loading={deleteLoading}
                danger
              >
                删除
              </Button>
            </Access>
            <Access key="assignPermission" accessible={canAssignPermissionToRole}>
              <Button
                type="link"
                className="inner-table-link"
                onClick={() => {
                  setCurrentRoleForPermission(record);
                  // 调用接口获取已分配权限
                  getPermissionByRoleId({ roleId: record.id! }).then((res) => {
                    if (res.code === 200 && res.data) {
                      const assignedIds = res.data.map((item) => item.id);
                      setPermissionTreeCheckedKeys(assignedIds as string[]);
                    }
                  });
                  setAssignPermissionModalVisible(true);
                }}
              >
                分配权限
              </Button>
            </Access>
          </Space>
        );
      },
    },
  ];
  return (
    <PageContainer header={{ title: false }}>
      <ProTable<API.RoleResponse>
        {...defaultTableConfig}
        scroll={{ x: '100%' }}
        rowKey="id"
        actionRef={tableRef}
        search={{
          labelWidth: 100,
          defaultCollapsed: false,
          filterType: 'query',
        }}
        columns={columns}
        headerTitle="角色列表"
        request={async (params) => queryPagingTable<API.listRoleParams>(params, listRole)}
        toolBarRender={() => [
          <Access key="add" accessible={canAddRole}>
            <Button
              type="primary"
              onClick={() => {
                setIsEditMode(false);
                setRoleModalVisible(true);
                roleForm.resetFields();
              }}
            >
              添加角色
            </Button>
          </Access>,
        ]}
      />
      {/* 分配用户 Modal */}
      <Modal
        title={`分配用户 - ${selectedRole.name}`}
        open={assignModalVisible}
        onCancel={() => setAssignModalVisible(false)}
        onOk={handleAssignSubmit}
        confirmLoading={assignLoading}
        destroyOnClose
      >
        <Form form={assignForm} layout="vertical">
          <Form.Item
            name="userIds"
            label="选择用户"
            rules={[{ required: true, message: '请选择用户' }]}
          >
            <Select mode="multiple" placeholder="请选择用户" loading={userListLoading} allowClear>
              {userList.map((user: API.UserVO) => (
                <Select.Option key={user.id} value={user.id}>
                  {user.username}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Form>
      </Modal>
      <Drawer
        title={`分配权限 - ${currentRoleForPermission.name}`}
        open={assignPermissionModalVisible}
        onClose={() => setAssignPermissionModalVisible(false)}
        width={500}
        extra={
          <Button type="primary" onClick={assignPermissionReq} loading={assignPermissionLoading}>
            提交
          </Button>
        }
        destroyOnClose
      >
        <Input.Search
          placeholder="搜索权限名称"
          value={searchPermission}
          onChange={(e) => setSearchPermission(e.target.value)}
          style={{ marginBottom: 8 }}
        />
        <Tree
          checkable
          treeData={filteredPermissionTreeData as DataNode[]}
          checkedKeys={permissionTreeCheckedKeys}
          onCheck={(checkedKeys) => setPermissionTreeCheckedKeys(checkedKeys as string[])}
          defaultExpandAll
        />
      </Drawer>
      <Modal
        title={isEditMode ? '更新子系统角色' : '添加子系统角色'}
        open={roleModalVisible}
        onCancel={() => setRoleModalVisible(false)}
        onOk={handleRoleSubmit}
        confirmLoading={roleLoading}
        destroyOnClose
      >
        <Form form={roleForm} layout="vertical">
          <Form.Item
            name="name"
            label="角色名称"
            rules={[{ required: true, message: '请输入角色名称' }]}
          >
            <Input placeholder="请输入角色名称" />
          </Form.Item>
          <Form.Item
            name="description"
            label="角色描述"
            rules={[{ required: true, message: '请输入角色描述' }]}
          >
            <Input.TextArea placeholder="请输入角色描述" />
          </Form.Item>
        </Form>
      </Modal>
    </PageContainer>
  );
};

export default ResourceGroup;
