import {
  getBaseLineDetailsListByRuleId,
  getBaseLineDetailsListByRuleIdAndTemplateId,
  updateBaseLineByInfo,
} from '@/services/sql-guard/rule';
import { defaultTableConfig } from '@/utils/setting';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Input, Modal, Radio, message } from 'antd';
import React, { useEffect } from 'react';

interface BaseLineDetailModalProps {
  open: boolean;
  onCancel: () => void;
  ruleId: string;
  templateId?: number;
  isAddPage?: boolean;
  isDetailPage?: boolean;
}

const BaseLineDetailModal: React.FC<BaseLineDetailModalProps> = ({
  open,
  onCancel,
  ruleId,
  templateId,
  isAddPage = false,
  isDetailPage = false,
}) => {
  const {
    data: baseLineDetails = [],
    loading,
    run: fetchData,
    mutate,
  } = useRequest(
    () =>
      isAddPage
        ? getBaseLineDetailsListByRuleId({ ruleId })
        : getBaseLineDetailsListByRuleIdAndTemplateId({ ruleId, templateId: templateId! }),
    {
      manual: true,
      formatResult: (res) => (res.code === 200 && res.data ? res.data : []),
    },
  );

  const { run: doUpdate } = useRequest(updateBaseLineByInfo, {
    manual: true,
    formatResult: (res) => res,
    onSuccess: (res, [, options]) => {
      if (res.code === 200 && options?.extra) {
        message.success('更新成功');
        const { record, newStandardPoint, newEnabledStatus } = options.extra;
        record.baseLineStandardPoint = newStandardPoint;
        record.enabledStatus = newEnabledStatus;
      }
    },
  });

  useEffect(() => {
    if (open && ruleId && (!templateId || isAddPage || templateId > 0)) {
      mutate([]);
      fetchData();
    }
  }, [open, ruleId, templateId]);

  const columns: ProColumns<API.RuleBaseLineDetailResponse>[] = [
    {
      title: '基线技术要求',
      dataIndex: 'baseLineRequirement',
      ellipsis: true,
      width: 200,
    },

    ...(!isAddPage && !isDetailPage
      ? [
          {
            title: (
              <div>
                基线标准点（参数）
                <span style={{ color: 'red' }}>（回车提交）</span>
              </div>
            ),
            dataIndex: 'baseLineStandardPoint',
            width: 220,
            render: (_: any, record: API.RuleBaseLineDetailResponse) => (
              <Input
                defaultValue={record.baseLineStandardPoint}
                onPressEnter={(e) => {
                  const newStandardPoint = (e.target as HTMLInputElement).value;
                  const newEnabledStatus = record.enabledStatus;
                  if (newStandardPoint !== record.baseLineStandardPoint) {
                    doUpdate(
                      {
                        templateId: templateId!.toString(),
                        ruleId,
                        baseLineId: record.id!,
                        baseLineStandardPoint: newStandardPoint,
                        enabledStatus: newEnabledStatus,
                      },
                      {
                        extra: { record, newStandardPoint, newEnabledStatus },
                      },
                    );
                  }
                }}
              />
            ),
          },
        ]
      : [
          {
            title: '基线标准点（参数）',
            dataIndex: 'baseLineStandardPoint',
            ellipsis: true,
            width: 200,
          },
        ]),

    {
      title: '说明',
      dataIndex: 'description',
      ellipsis: true,
      width: 200,
    },

    ...(!isAddPage && !isDetailPage
      ? [
          {
            title: (
              <div>
                启用状态
                <span style={{ color: 'red' }}>（点击提交）</span>
              </div>
            ),
            dataIndex: 'enabledStatus',
            width: 180,
            render: (_, record) => (
              <Radio.Group
                value={record.enabledStatus}
                onChange={(e) => {
                  const newEnabledStatus = e.target.value;
                  const newBaseLineStandardPoint = record.baseLineStandardPoint;

                  if (newEnabledStatus !== record.enabledStatus) {
                    doUpdate(
                      {
                        templateId: templateId!.toString(),
                        ruleId,
                        baseLineId: record.id!,
                        baseLineStandardPoint: newBaseLineStandardPoint,
                        enabledStatus: newEnabledStatus,
                      },
                      {
                        extra: { record, newBaseLineStandardPoint, newEnabledStatus },
                      },
                    );
                  }
                }}
              >
                <Radio value={1}>启用</Radio>
                <Radio value={2}>禁用</Radio>
              </Radio.Group>
            ),
          } as ProColumns<API.RuleBaseLineDetailResponse>,
        ]
      : [
          {
            title: '启用状态',
            dataIndex: 'enabledStatus',
            width: 100,
            valueEnum: {
              1: { text: '启用', status: 'Success' },
              2: { text: '禁用', status: 'Error' },
            },
          } as ProColumns<API.RuleBaseLineDetailResponse>,
        ]),
  ];

  return (
    <Modal
      title={!isAddPage && !isDetailPage ? '编辑基线要求' : '查看基线要求'}
      open={open}
      onCancel={onCancel}
      width={900}
      footer={null}
      destroyOnClose
    >
      <ProTable<API.RuleBaseLineDetailResponse>
        {...defaultTableConfig}
        columns={columns}
        dataSource={baseLineDetails}
        loading={loading}
        search={false}
        options={false}
        pagination={false}
        footer={() => <div style={{ textAlign: 'right' }}>共 {baseLineDetails.length} 条</div>}
      />
    </Modal>
  );
};

export default BaseLineDetailModal;
