import { DATASOURCE_TYPE, RULE_AUDIT_MODE } from '@/enums';
import { deleteRuleTemplate, listRuleTemplate } from '@/services/sql-guard/ruleTemplate';
import { option2enum, queryPagingTable } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { Access, history, useAccess, useRequest } from '@umijs/max';
import { Button, Modal, Space, Tag, message } from 'antd';
import { useRef } from 'react';

const RulesTemplate: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const {
    canAddTemplate = false,
    canUpdateTemplate = false,
    canDeleteTemplate = false,
  } = useAccess();

  const { run: deleteRecord } = useRequest((id) => deleteRuleTemplate(id), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success(res.message);
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });
  const handleDelete = async (row: API.RuleTemplateResponse) => {
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除“${row.templateName!}”吗?`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord({ id: row.id! });
      },
    });
  };

  const onEdit = (record: API.RuleTemplateResponse) =>
    history.push(`/basic/rules-template/edit/${record.id}/${record.dbType}`);

  const columns: ProColumns<API.RuleTemplateResponse>[] = [
    {
      title: '策略名称',
      dataIndex: 'templateName',
      fixed: 'left',
      width: 150,
      ellipsis: true,
      copyable: true,
      render: (dom, record) => {
        return (
          <a
            className="rk-a-span"
            onClick={() =>
              history.push(`/basic/rules-template/detail/${record.id}/${record.dbType}`)
            }
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: '审核类型',
      dataIndex: 'auditMode',
      valueType: 'select',
      width: 100,
      ellipsis: true,
      initialValue: String(RULE_AUDIT_MODE[0]?.value),
      valueEnum: option2enum(RULE_AUDIT_MODE),
      fieldProps: (form) => ({
        allowClear: false,
        showSearch: true,
        filterOption: true,
        optionFilterProp: 'label',
        onChange: (val) => {
          if (Number(val) === 3) {
            form?.setFieldValue('dbType', '301');
          } else {
            form?.setFieldValue('dbType', '1');
          }
        },
      }),
      //   fieldProps: {
      //     showSearch: true,
      //     filterOption: true,
      //     optionFilterProp: 'label',
      //     defaultValue: '1',
      //   },
    },
    {
      title: '对象类型',
      dataIndex: 'dbType',
      valueType: 'select',
      //   valueEnum: option2enum(DATASOURCE_TYPE),
      dependencies: ['auditMode'],
      width: 170,
      ellipsis: true,
      initialValue: String(
        __FEATURE_TOGGLE__.defaultObjectTypeByAudit?.[RULE_AUDIT_MODE[0]?.value],
      ),
      fieldProps: (form) => {
        const auditMode = Number(form?.getFieldValue?.('auditMode'));
        let filteredOptions: typeof DATASOURCE_TYPE = [];

        if (auditMode === 3) {
          filteredOptions = DATASOURCE_TYPE.filter((item) => {
            const itemValue = Number(item.value);
            return itemValue >= 301 && itemValue <= 308;
          });
        } else {
          filteredOptions = DATASOURCE_TYPE.filter((item) => {
            const itemValue = Number(item.value);
            return itemValue >= 1 && itemValue <= 6;
          });
        }

        return {
          allowClear: false,
          showSearch: true,
          filterOption: true,
          optionFilterProp: 'label',
          options: filteredOptions,
          placeholder: '请选择对象类型',
        };
      },
      renderText: (text, record) => {
        const obj = DATASOURCE_TYPE.find((item) => Number(item.value) === record.dbType);
        return <Tag color={obj?.color}>{obj?.label}</Tag>;
      },
    },
    {
      title: '是否为系统策略',
      dataIndex: 'isSystemTemplateName',
      width: 120,
      hideInSearch: true,
    },
    {
      title: '策略说明',
      dataIndex: 'description',
      width: 300,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '操作',
      width: 150,
      key: 'option',
      fixed: 'right',
      valueType: 'option',
      align: 'center',
      render: (text, record) => {
        return (
          <Space>
            {record.isSystemTemplate !== 1 && (
              <>
                <Access key="edit" accessible={canUpdateTemplate}>
                  <a onClick={() => onEdit(record)}>编辑</a>
                </Access>
                <Access key="del" accessible={canDeleteTemplate}>
                  <a onClick={() => handleDelete(record)}>删除</a>
                </Access>
              </>
            )}
          </Space>
        );
      },
    },
  ];
  return (
    <PageContainer header={{ title: false }}>
      <ProTable
        {...defaultTableConfig}
        actionRef={tableRef}
        scroll={{ x: '100%' }}
        columns={columns}
        headerTitle="策略列表"
        search={{
          filterType: 'query',
          defaultCollapsed: false,
        }}
        toolbar={{
          actions: [
            <Access key="add" accessible={canAddTemplate}>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  history.push('/basic/rules-template/add');
                }}
              >
                新建策略
              </Button>
            </Access>,
          ],
        }}
        request={async (params) =>
          queryPagingTable<API.listRuleTemplateParams>({ ...params }, listRuleTemplate)
        }
      />
    </PageContainer>
  );
};

export default RulesTemplate;
