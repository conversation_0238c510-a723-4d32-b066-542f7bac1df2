import RkPie from '@/components/Charts/Pie';
import { dashboardIndex } from '@/services/sql-guard/dashboard';
import { renderNumber } from '@/utils';
import { PageContainer } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Card, Col, Row } from 'antd';

const StaticAuditStatus = () => {
  const { data, loading } = useRequest<any>(dashboardIndex);

  const auditCountStaticType1 = data?.auditCountStaticList?.find(
    (i: API.DashboardAuditCountStatic) => i.dbType === 1,
  );
  const auditCountStaticType2 = data?.auditCountStaticList?.find(
    (i: API.DashboardAuditCountStatic) => i.dbType === 2,
  );
  const auditCountStaticType3 = data?.auditCountStaticList?.find(
    (i: API.DashboardAuditCountStatic) => i.dbType === 3,
  );
  const auditCountStaticType4 = data?.auditCountStaticList?.find(
    (i: API.DashboardAuditCountStatic) => i.dbType === 4,
  );
  const auditCountStaticType5 = data?.auditCountStaticList?.find(
    (i: API.DashboardAuditCountStatic) => i.dbType === 5,
  );
  const auditCountStaticType6 = data?.auditCountStaticList?.find(
    (i: API.DashboardAuditCountStatic) => i.dbType === 6,
  );

  return (
    <PageContainer header={{ title: '静态审核状态详情' }} loading={loading}>
      <Card bordered={false}>
        <Row gutter={12}>
          <Col span={8}>
            <RkPie
              color={['#83BC99', '#ED7B7B']}
              height={220}
              radius={0.8}
              innerRadius={0.6}
              showPercentValue
              innerTitle={'MySQL'}
              label={{
                type: 'inner',
                content: '{value}',
              }}
              innerContent={auditCountStaticType1?.totalAuditCountSql || 0}
              data={[
                {
                  type: '成功次数',
                  value: renderNumber(auditCountStaticType1?.auditPassedCountSql),
                },
                {
                  type: '失败次数',
                  value: renderNumber(auditCountStaticType1?.auditFailedCountSql),
                },
              ]}
            />
          </Col>
          <Col span={8}>
            <RkPie
              color={['#83BC99', '#ED7B7B']}
              height={220}
              radius={0.8}
              innerRadius={0.6}
              showPercentValue
              innerTitle={'Oracle'}
              label={{
                type: 'inner',
                content: '{value}',
              }}
              innerContent={auditCountStaticType2?.totalAuditCountSql || 0}
              data={[
                {
                  type: '成功次数',
                  value: renderNumber(auditCountStaticType2?.auditPassedCountSql),
                },
                {
                  type: '失败次数',
                  value: renderNumber(auditCountStaticType2?.auditFailedCountSql),
                },
              ]}
            />
          </Col>
          <Col span={8}>
            <RkPie
              color={['#83BC99', '#ED7B7B']}
              height={220}
              radius={0.8}
              innerRadius={0.6}
              showPercentValue
              innerTitle={'TDSQL'}
              label={{
                type: 'inner',
                content: '{value}',
              }}
              innerContent={auditCountStaticType3?.totalAuditCountSql || 0}
              data={[
                {
                  type: '成功次数',
                  value: renderNumber(auditCountStaticType3?.auditPassedCountSql),
                },
                {
                  type: '失败次数',
                  value: renderNumber(auditCountStaticType3?.auditFailedCountSql),
                },
              ]}
            />
          </Col>
          <Col span={8}>
            <RkPie
              color={['#83BC99', '#ED7B7B']}
              height={220}
              radius={0.8}
              innerRadius={0.6}
              showPercentValue
              innerTitle={'DM'}
              label={{
                type: 'inner',
                content: '{value}',
              }}
              innerContent={auditCountStaticType4?.totalAuditCountSql || 0}
              data={[
                {
                  type: '成功次数',
                  value: renderNumber(auditCountStaticType4?.auditPassedCountSql),
                },
                {
                  type: '失败次数',
                  value: renderNumber(auditCountStaticType4?.auditFailedCountSql),
                },
              ]}
            />
          </Col>
          <Col span={8}>
            <RkPie
              color={['#83BC99', '#ED7B7B']}
              height={220}
              radius={0.8}
              innerRadius={0.6}
              showPercentValue
              innerTitle={'OB(MySql兼容)'}
              innerTitleOffsetX={-8}
              innerTitleSize={'10px'}
              label={{
                type: 'inner',
                content: '{value}',
              }}
              innerContent={auditCountStaticType5?.totalAuditCountSql || 0}
              data={[
                {
                  type: '成功次数',
                  value: renderNumber(auditCountStaticType5?.auditPassedCountSql),
                },
                {
                  type: '失败次数',
                  value: renderNumber(auditCountStaticType5?.auditFailedCountSql),
                },
              ]}
            />
          </Col>
          <Col span={8}>
            <RkPie
              color={['#83BC99', '#ED7B7B']}
              height={220}
              radius={0.8}
              innerRadius={0.6}
              showPercentValue
              innerTitle={'OB(Oracle兼容)'}
              innerTitleOffsetX={-10}
              innerTitleSize={'10px'}
              label={{
                type: 'inner',
                content: '{value}',
              }}
              innerContent={auditCountStaticType6?.totalAuditCountSql || 0}
              data={[
                {
                  type: '成功次数',
                  value: renderNumber(auditCountStaticType6?.auditPassedCountSql),
                },
                {
                  type: '失败次数',
                  value: renderNumber(auditCountStaticType6?.auditFailedCountSql),
                },
              ]}
            />
          </Col>
        </Row>
      </Card>
    </PageContainer>
  );
};

export default StaticAuditStatus;
