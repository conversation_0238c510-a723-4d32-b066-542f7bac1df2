import RkPie from '@/components/Charts/Pie';
import { dashboardIndex } from '@/services/sql-guard/dashboard';
import { renderNumber } from '@/utils';
import { PageContainer } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Card, Col, Row } from 'antd';

const BaselineAuditStatus = () => {
  const { data, loading } = useRequest<any>(dashboardIndex);

  const auditCountBaseLineType301 = data?.auditCountBaseLineList?.find(
    (i: API.DashboardAuditCountBaseLine) => i.dbType === 301,
  );
  const auditCountBaseLineType302 = data?.auditCountBaseLineList?.find(
    (i: API.DashboardAuditCountBaseLine) => i.dbType === 302,
  );
  const auditCountBaseLineType303 = data?.auditCountBaseLineList?.find(
    (i: API.DashboardAuditCountBaseLine) => i.dbType === 303,
  );
  const auditCountBaseLineType304 = data?.auditCountBaseLineList?.find(
    (i: API.DashboardAuditCountBaseLine) => i.dbType === 304,
  );
  const auditCountBaseLineType305 = data?.auditCountBaseLineList?.find(
    (i: API.DashboardAuditCountBaseLine) => i.dbType === 305,
  );
  const auditCountBaseLineType306 = data?.auditCountBaseLineList?.find(
    (i: API.DashboardAuditCountBaseLine) => i.dbType === 306,
  );
  const auditCountBaseLineType307 = data?.auditCountBaseLineList?.find(
    (i: API.DashboardAuditCountBaseLine) => i.dbType === 307,
  );
  const auditCountBaseLineType308 = data?.auditCountBaseLineList?.find(
    (i: API.DashboardAuditCountBaseLine) => i.dbType === 308,
  );

  return (
    <PageContainer header={{ title: '基线审核状态详情' }} loading={loading}>
      <Card bordered={false}>
        <Row gutter={12}>
          <Col span={8} style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
            <RkPie
              color={['#83BC99', '#ED7B7B']}
              height={220}
              radius={0.8}
              innerRadius={0.6}
              legend={false}
              label={{
                type: 'inner',
                style: {
                  fontSize: 10, // 设置标签字体大小
                },
              }}
              statistic={{
                title: false,
                content: {
                  style: {
                    fontSize: 14 as unknown as string,
                  },
                },
              }}
              innerContent={auditCountBaseLineType301?.totalAuditCountSql || 0}
              data={[
                {
                  type: '成功次数',
                  value: renderNumber(auditCountBaseLineType301?.auditPassedCountSql),
                },
                {
                  type: '失败次数',
                  value: renderNumber(auditCountBaseLineType301?.auditFailedCountSql),
                },
              ]}
            />
            <div>Windows操作系统</div>
          </Col>
          <Col span={8} style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
            <RkPie
              color={['#83BC99', '#ED7B7B']}
              height={220}
              radius={0.8}
              innerRadius={0.6}
              legend={false}
              label={{
                type: 'inner',
                style: {
                  fontSize: 10, // 设置标签字体大小
                },
              }}
              statistic={{
                title: false,
                content: {
                  style: {
                    fontSize: 14 as unknown as string,
                  },
                },
              }}
              innerContent={auditCountBaseLineType302?.totalAuditCountSql || 0}
              data={[
                {
                  type: '成功次数',
                  value: renderNumber(auditCountBaseLineType302?.auditPassedCountSql),
                },
                {
                  type: '失败次数',
                  value: renderNumber(auditCountBaseLineType302?.auditFailedCountSql),
                },
              ]}
            />
            <div>Linux操作系统</div>
          </Col>
          <Col span={8} style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
            <RkPie
              color={['#83BC99', '#ED7B7B']}
              height={220}
              radius={0.8}
              innerRadius={0.6}
              legend={false}
              label={{
                type: 'inner',
                style: {
                  fontSize: 10, // 设置标签字体大小
                },
              }}
              statistic={{
                title: false,
                content: {
                  style: {
                    fontSize: 14 as unknown as string,
                  },
                },
              }}
              innerContent={auditCountBaseLineType303?.totalAuditCountSql || 0}
              data={[
                {
                  type: '成功次数',
                  value: renderNumber(auditCountBaseLineType303?.auditPassedCountSql),
                },
                {
                  type: '失败次数',
                  value: renderNumber(auditCountBaseLineType303?.auditFailedCountSql),
                },
              ]}
            />
            <div>Oracle数据库(基线)</div>
          </Col>
          <Col span={8} style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
            <RkPie
              color={['#83BC99', '#ED7B7B']}
              height={220}
              radius={0.8}
              innerRadius={0.6}
              legend={false}
              label={{
                type: 'inner',
                style: {
                  fontSize: 10, // 设置标签字体大小
                },
              }}
              statistic={{
                title: false,
                content: {
                  style: {
                    fontSize: 14 as unknown as string,
                  },
                },
              }}
              innerContent={auditCountBaseLineType304?.totalAuditCountSql || 0}
              data={[
                {
                  type: '成功次数',
                  value: renderNumber(auditCountBaseLineType304?.auditPassedCountSql),
                },
                {
                  type: '失败次数',
                  value: renderNumber(auditCountBaseLineType304?.auditFailedCountSql),
                },
              ]}
            />
            <div>DM(达梦)数据库(基线)</div>
          </Col>
          <Col span={8} style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
            <RkPie
              color={['#83BC99', '#ED7B7B']}
              height={220}
              radius={0.8}
              innerRadius={0.6}
              legend={false}
              label={{
                type: 'inner',
                style: {
                  fontSize: 10, // 设置标签字体大小
                },
              }}
              statistic={{
                title: false,
                content: {
                  style: {
                    fontSize: 14 as unknown as string,
                  },
                },
              }}
              innerContent={auditCountBaseLineType305?.totalAuditCountSql || 0}
              data={[
                {
                  type: '成功次数',
                  value: renderNumber(auditCountBaseLineType305?.auditPassedCountSql),
                },
                {
                  type: '失败次数',
                  value: renderNumber(auditCountBaseLineType305?.auditFailedCountSql),
                },
              ]}
            />
            <div>Apache中间件</div>
          </Col>
          <Col span={8} style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
            <RkPie
              color={['#83BC99', '#ED7B7B']}
              height={220}
              radius={0.8}
              innerRadius={0.6}
              legend={false}
              label={{
                type: 'inner',
                style: {
                  fontSize: 10, // 设置标签字体大小
                },
              }}
              statistic={{
                title: false,
                content: {
                  style: {
                    fontSize: 14 as unknown as string,
                  },
                },
              }}
              innerContent={auditCountBaseLineType306?.totalAuditCountSql || 0}
              data={[
                {
                  type: '成功次数',
                  value: renderNumber(auditCountBaseLineType306?.auditPassedCountSql),
                },
                {
                  type: '失败次数',
                  value: renderNumber(auditCountBaseLineType306?.auditFailedCountSql),
                },
              ]}
            />
            <div>Tomcat中间件</div>
          </Col>
          <Col span={8} style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
            <RkPie
              color={['#83BC99', '#ED7B7B']}
              height={220}
              radius={0.8}
              innerRadius={0.6}
              legend={false}
              label={{
                type: 'inner',
                style: {
                  fontSize: 10, // 设置标签字体大小
                },
              }}
              statistic={{
                title: false,
                content: {
                  style: {
                    fontSize: 14 as unknown as string,
                  },
                },
              }}
              innerContent={auditCountBaseLineType307?.totalAuditCountSql || 0}
              data={[
                {
                  type: '成功次数',
                  value: renderNumber(auditCountBaseLineType307?.auditPassedCountSql),
                },
                {
                  type: '失败次数',
                  value: renderNumber(auditCountBaseLineType307?.auditFailedCountSql),
                },
              ]}
            />
            <div>NGINX中间件</div>
          </Col>
          <Col span={8} style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
            <RkPie
              color={['#83BC99', '#ED7B7B']}
              height={220}
              radius={0.8}
              innerRadius={0.6}
              legend={false}
              label={{
                type: 'inner',
                style: {
                  fontSize: 10, // 设置标签字体大小
                },
              }}
              statistic={{
                title: false,
                content: {
                  style: {
                    fontSize: 14 as unknown as string,
                  },
                },
              }}
              innerContent={auditCountBaseLineType308?.totalAuditCountSql || 0}
              data={[
                {
                  type: '成功次数',
                  value: renderNumber(auditCountBaseLineType308?.auditPassedCountSql),
                },
                {
                  type: '失败次数',
                  value: renderNumber(auditCountBaseLineType308?.auditFailedCountSql),
                },
              ]}
            />
            <div>Redis中间件</div>
          </Col>
        </Row>
      </Card>
    </PageContainer>
  );
};

export default BaselineAuditStatus;
