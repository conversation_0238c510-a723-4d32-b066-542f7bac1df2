import RkBar from '@/components/Charts/Bar';
import RKColumn from '@/components/Charts/Column';
import RkPie from '@/components/Charts/Pie';
import { DATASOURCE_TYPE, RULE_AUDIT_MODE, WARN_LEVEL } from '@/enums';
import { dashboardIndex } from '@/services/sql-guard/dashboard';
import { renderNumber } from '@/utils';
import { LineChartOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { history, useRequest } from '@umijs/max';
import { Badge, Button, Card, Col, Row } from 'antd';

const Dashboard = () => {
  const { data, loading } = useRequest<any>(dashboardIndex);

  const auditCountSqlType1 = data?.auditCountSqlList?.find(
    (i: API.DashboardAuditCountSql) => i.dbType === 1,
  );
  const auditCountSqlType2 = data?.auditCountSqlList?.find(
    (i: API.DashboardAuditCountSql) => i.dbType === 2,
  );
  const auditCountSqlType3 = data?.auditCountSqlList?.find(
    (i: API.DashboardAuditCountSql) => i.dbType === 3,
  );
  const auditCountSqlType4 = data?.auditCountSqlList?.find(
    (i: API.DashboardAuditCountSql) => i.dbType === 4,
  );

  const auditCountBaseLineType301 = data?.auditCountBaseLineList?.find(
    (i: API.DashboardAuditCountBaseLine) => i.dbType === 301,
  );
  const auditCountBaseLineType302 = data?.auditCountBaseLineList?.find(
    (i: API.DashboardAuditCountBaseLine) => i.dbType === 302,
  );
  const auditCountBaseLineType303 = data?.auditCountBaseLineList?.find(
    (i: API.DashboardAuditCountBaseLine) => i.dbType === 303,
  );
  const auditCountBaseLineType304 = data?.auditCountBaseLineList?.find(
    (i: API.DashboardAuditCountBaseLine) => i.dbType === 304,
  );

  const auditCountStaticType1 = data?.auditCountStaticList?.find(
    (i: API.DashboardAuditCountStatic) => i.dbType === 1,
  );
  const auditCountStaticType2 = data?.auditCountStaticList?.find(
    (i: API.DashboardAuditCountStatic) => i.dbType === 2,
  );
  const auditCountStaticType3 = data?.auditCountStaticList?.find(
    (i: API.DashboardAuditCountStatic) => i.dbType === 3,
  );
  const auditCountStaticType4 = data?.auditCountStaticList?.find(
    (i: API.DashboardAuditCountStatic) => i.dbType === 4,
  );

  return (
    <PageContainer header={{ title: false }} loading={loading}>
      <Row gutter={[24, 24]}>
        {/* 规则数量模块 - 优化后 */}
        <Col span={12}>
          <Card title="规则数量" bordered={false}>
            <Row style={{ display: 'flex', justifyContent: 'space-between', height: 150 }}>
              {RULE_AUDIT_MODE.map((mode) => {
                // 过滤当前模式的数据
                const modeData = (data?.dashboardRuleCountList || []).filter(
                  (item: API.DashboardRuleCount) => item.auditMode === mode.value,
                );

                // 计算总数
                const totalCount = modeData.reduce(
                  (sum: number, item: API.DashboardRuleCount) => sum + (item.count || 0),
                  0,
                );

                // 组织级别数据
                const levelData = [
                  {
                    level: 1,
                    count:
                      modeData.find((d: API.DashboardRuleCount) => d.alertLevel === 1)?.count || 0,
                  },
                  {
                    level: 2,
                    count:
                      modeData.find((d: API.DashboardRuleCount) => d.alertLevel === 2)?.count || 0,
                  },
                  {
                    level: 3,
                    count:
                      modeData.find((d: API.DashboardRuleCount) => d.alertLevel === 3)?.count || 0,
                  },
                ];

                return (
                  <Col
                    flex="1"
                    key={mode.value}
                    style={{
                      padding: '0 30px',
                      borderRight:
                        mode.value !== RULE_AUDIT_MODE[RULE_AUDIT_MODE.length - 1].value
                          ? '1px solid #f0f0f0'
                          : 'none',
                    }}
                  >
                    <div style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
                      {/* 标题行 */}
                      <div
                        style={{
                          flex: 1,
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                        }}
                      >
                        <span>
                          <LineChartOutlined
                            style={{
                              fontSize: 18,
                              marginRight: 10,
                              background: 'var(--primary-color)',
                              color: '#fff',
                              padding: 5,
                              borderRadius: 3,
                            }}
                          />
                          {mode.label}规则
                        </span>
                        <span style={{ fontWeight: 'bold', fontSize: 16 }}>{totalCount}</span>
                      </div>

                      {/* 级别数据 */}
                      <div style={{ flex: 1, display: 'flex', justifyContent: 'space-between' }}>
                        {levelData.map(({ level, count }) => {
                          const levelInfo = WARN_LEVEL.find(
                            (l: { value: string; label: string }) => l.value === String(level),
                          );
                          return (
                            <div
                              key={level}
                              style={{
                                display: 'flex',
                                alignItems: 'center',
                              }}
                            >
                              <Badge
                                status={level === 1 ? 'success' : level === 2 ? 'warning' : 'error'}
                                text={levelInfo?.label}
                                style={{ marginRight: 8 }}
                              />
                              <span style={{ fontSize: 16 }}>{count}</span>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  </Col>
                );
              })}
            </Row>
          </Card>
        </Col>
        <Col span={12}>
          <Card title="审核次数汇总" bordered={false}>
            <RkPie
              appendPadding={10}
              height={118}
              radius={0.85}
              legend={false} // 关闭图例
              startAngle={Math.PI}
              endAngle={Math.PI * 1.5}
              showPercentValue
              tooltip={false}
              label={{
                type: 'spider',
                content: '{name}:{value}',
              }}
              data={
                data?.auditTimeList
                  ?.filter((item: { auditMode: number }) => item.auditMode !== 99)
                  .map((item: { auditMode: number; auditTime: number }) => ({
                    type:
                      RULE_AUDIT_MODE.find((mode) => mode.value === item.auditMode)?.label ||
                      '未知',
                    value: renderNumber(item.auditTime),
                  })) || []
              }
            />
            <div style={{ fontWeight: 'bold', marginTop: 10, textAlign: 'center' }}>
              审核总次数：
              {data?.auditTimeList?.find((item: { auditMode: number }) => item.auditMode === 99)
                ?.auditTime || 0}
            </div>
          </Card>
        </Col>
        <Col span={12}>
          <Card title="违反排名前10规则" bordered={false}>
            <RkBar
              height={364}
              xField="violationCount"
              yField="ruleName"
              color={'#FF6969'}
              meta={{
                ruleName: {
                  alias: '规则名',
                },
                violationCount: {
                  alias: '违反次数',
                },
              }}
              xAxis={{
                nice: false,
              }}
              yAxis={{
                label: {
                  autoRotate: false,
                  formatter: (text: string) => {
                    return text.length > 25 ? `${text.slice(0, 25)}\n${text.slice(25)}` : text;
                  },
                },
              }}
              barWidthRatio={data?.auditViolatedRuleList?.length < 10 ? 0.1 : 0.5}
              data={
                data?.auditViolatedRuleList?.filter(
                  (i: API.DashboardViolatedRule) =>
                    (i?.violationCount || i?.violationCount === 0) && i?.ruleName,
                ) || []
              }
            />
          </Card>
        </Col>

        {__ENABLE_DYNAMIC_AUDIT_ENABLED__ && (
          <Col span={12}>
            <Card
              title="动态审核状态"
              bordered={false}
              extra={
                <Button type="link" onClick={() => history.push('/dashboard/sqlAuditStatus')}>
                  更多
                </Button>
              }
            >
              <Row gutter={12}>
                <Col span={12}>
                  <RkPie
                    color={['#83BC99', '#ED7B7B']}
                    height={182}
                    radius={0.8}
                    innerRadius={0.6}
                    showPercentValue
                    innerTitle={'MySQL'}
                    label={{
                      type: 'inner',
                      content: '{value}',
                    }}
                    innerContent={auditCountSqlType1?.totalAuditCountSql || 0}
                    data={[
                      {
                        type: '成功次数',
                        value: renderNumber(auditCountSqlType1?.auditPassedCountSql),
                      },
                      {
                        type: '失败次数',
                        value: renderNumber(auditCountSqlType1?.auditFailedCountSql),
                      },
                    ]}
                  />
                </Col>
                <Col span={12}>
                  <RkPie
                    color={['#83BC99', '#ED7B7B']}
                    height={182}
                    radius={0.8}
                    innerRadius={0.6}
                    showPercentValue
                    innerTitle={'Oracle'}
                    label={{
                      type: 'inner',
                      content: '{value}',
                    }}
                    innerContent={auditCountSqlType2?.totalAuditCountSql || 0}
                    data={[
                      {
                        type: '成功次数',
                        value: renderNumber(auditCountSqlType2?.auditPassedCountSql),
                      },
                      {
                        type: '失败次数',
                        value: renderNumber(auditCountSqlType2?.auditFailedCountSql),
                      },
                    ]}
                  />
                </Col>
                <Col span={12}>
                  <RkPie
                    color={['#83BC99', '#ED7B7B']}
                    height={182}
                    radius={0.8}
                    innerRadius={0.6}
                    showPercentValue
                    innerTitle={'TDSQL'}
                    label={{
                      type: 'inner',
                      content: '{value}',
                    }}
                    innerContent={auditCountSqlType3?.totalAuditCountSql || 0}
                    data={[
                      {
                        type: '成功次数',
                        value: renderNumber(auditCountSqlType3?.auditPassedCountSql),
                      },
                      {
                        type: '失败次数',
                        value: renderNumber(auditCountSqlType3?.auditFailedCountSql),
                      },
                    ]}
                  />
                </Col>
                <Col span={12}>
                  <RkPie
                    color={['#83BC99', '#ED7B7B']}
                    height={182}
                    radius={0.8}
                    innerRadius={0.6}
                    showPercentValue
                    innerTitle={'DM'}
                    label={{
                      type: 'inner',
                      content: '{value}',
                    }}
                    innerContent={auditCountSqlType4?.totalAuditCountSql || 0}
                    data={[
                      {
                        type: '成功次数',
                        value: renderNumber(auditCountSqlType4?.auditPassedCountSql),
                      },
                      {
                        type: '失败次数',
                        value: renderNumber(auditCountSqlType4?.auditFailedCountSql),
                      },
                    ]}
                  />
                </Col>
              </Row>
            </Card>
          </Col>
        )}

        {__ENABLE_BASELINE_AUDIT_ENABLED__ && (
          <Col span={12}>
            <Card
              title="基线审核状态"
              bordered={false}
              extra={
                <Button type="link" onClick={() => history.push('/dashboard/baselineAuditStatus')}>
                  更多
                </Button>
              }
            >
              <Row gutter={12}>
                <Col
                  span={12}
                  style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}
                >
                  <RkPie
                    color={['#83BC99', '#ED7B7B']}
                    height={160}
                    radius={0.8}
                    innerRadius={0.5}
                    legend={false}
                    label={{
                      type: 'inner',
                      style: {
                        fontSize: 10, // 设置标签字体大小
                      },
                    }}
                    statistic={{
                      title: false,
                      content: {
                        style: {
                          fontSize: 10 as unknown as string,
                        },
                      },
                    }}
                    innerContent={auditCountBaseLineType301?.totalAuditCountSql || 0}
                    data={[
                      {
                        type: '成功次数',
                        value: renderNumber(auditCountBaseLineType301?.auditPassedCountSql),
                      },
                      {
                        type: '失败次数',
                        value: renderNumber(auditCountBaseLineType301?.auditFailedCountSql),
                      },
                    ]}
                  />
                  <div>Windows操作系统</div>
                </Col>
                <Col
                  span={12}
                  style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}
                >
                  <RkPie
                    color={['#83BC99', '#ED7B7B']}
                    height={160}
                    radius={0.8}
                    innerRadius={0.5}
                    legend={false}
                    label={{
                      type: 'inner',
                      style: {
                        fontSize: 10, // 设置标签字体大小
                      },
                    }}
                    statistic={{
                      title: false,
                      content: {
                        style: {
                          fontSize: 10 as unknown as string,
                        },
                      },
                    }}
                    innerContent={auditCountBaseLineType302?.totalAuditCountSql || 0}
                    data={[
                      {
                        type: '成功次数',
                        value: renderNumber(auditCountBaseLineType302?.auditPassedCountSql),
                      },
                      {
                        type: '失败次数',
                        value: renderNumber(auditCountBaseLineType302?.auditFailedCountSql),
                      },
                    ]}
                  />
                  <div>Linux操作系统</div>
                </Col>
                <Col
                  span={12}
                  style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}
                >
                  <RkPie
                    color={['#83BC99', '#ED7B7B']}
                    height={160}
                    radius={0.8}
                    innerRadius={0.5}
                    legend={false}
                    label={{
                      type: 'inner',
                      style: {
                        fontSize: 10, // 设置标签字体大小
                      },
                    }}
                    statistic={{
                      title: false,
                      content: {
                        style: {
                          fontSize: 10 as unknown as string,
                        },
                      },
                    }}
                    innerContent={auditCountBaseLineType303?.totalAuditCountSql || 0}
                    data={[
                      {
                        type: '成功次数',
                        value: renderNumber(auditCountBaseLineType303?.auditPassedCountSql),
                      },
                      {
                        type: '失败次数',
                        value: renderNumber(auditCountBaseLineType303?.auditFailedCountSql),
                      },
                    ]}
                  />
                  <div>Oracle数据库(基线)</div>
                </Col>
                <Col
                  span={12}
                  style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}
                >
                  <RkPie
                    color={['#83BC99', '#ED7B7B']}
                    height={160}
                    radius={0.8}
                    innerRadius={0.5}
                    legend={false}
                    label={{
                      type: 'inner',
                      style: {
                        fontSize: 10, // 设置标签字体大小
                      },
                    }}
                    statistic={{
                      title: false,
                      content: {
                        style: {
                          fontSize: 10 as unknown as string,
                        },
                      },
                    }}
                    innerContent={auditCountBaseLineType304?.totalAuditCountSql || 0}
                    data={[
                      {
                        type: '成功次数',
                        value: renderNumber(auditCountBaseLineType304?.auditPassedCountSql),
                      },
                      {
                        type: '失败次数',
                        value: renderNumber(auditCountBaseLineType304?.auditFailedCountSql),
                      },
                    ]}
                  />
                  <div>DM(达梦)数据库(基线)</div>
                </Col>
              </Row>
            </Card>
          </Col>
        )}

        {__ENABLE_STATIC_AUDIT_ENABLED__ && (
          <Col span={12}>
            <Card
              title="静态审核状态"
              bordered={false}
              extra={
                <Button type="link" onClick={() => history.push('/dashboard/staticAuditStatus')}>
                  更多
                </Button>
              }
            >
              <Row gutter={12}>
                <Col span={12}>
                  <RkPie
                    color={['#83BC99', '#ED7B7B']}
                    height={180}
                    radius={0.8}
                    innerRadius={0.6}
                    showPercentValue
                    innerTitle={'MySQL'}
                    label={{
                      type: 'inner',
                      content: '{value}',
                    }}
                    innerContent={auditCountStaticType1?.totalAuditCountSql || 0}
                    data={[
                      {
                        type: '成功次数',
                        value: renderNumber(auditCountStaticType1?.auditPassedCountSql),
                      },
                      {
                        type: '失败次数',
                        value: renderNumber(auditCountStaticType1?.auditFailedCountSql),
                      },
                    ]}
                  />
                </Col>
                <Col span={12}>
                  <RkPie
                    color={['#83BC99', '#ED7B7B']}
                    height={180}
                    radius={0.8}
                    innerRadius={0.6}
                    showPercentValue
                    innerTitle={'Oracle'}
                    label={{
                      type: 'inner',
                      content: '{value}',
                    }}
                    innerContent={auditCountStaticType2?.totalAuditCountSql || 0}
                    data={[
                      {
                        type: '成功次数',
                        value: renderNumber(auditCountStaticType2?.auditPassedCountSql),
                      },
                      {
                        type: '失败次数',
                        value: renderNumber(auditCountStaticType2?.auditFailedCountSql),
                      },
                    ]}
                  />
                </Col>
                <Col span={12}>
                  <RkPie
                    color={['#83BC99', '#ED7B7B']}
                    height={180}
                    radius={0.8}
                    innerRadius={0.6}
                    showPercentValue
                    innerTitle={'TDSQL'}
                    label={{
                      type: 'inner',
                      content: '{value}',
                    }}
                    innerContent={auditCountStaticType3?.totalAuditCountSql || 0}
                    data={[
                      {
                        type: '成功次数',
                        value: renderNumber(auditCountStaticType3?.auditPassedCountSql),
                      },
                      {
                        type: '失败次数',
                        value: renderNumber(auditCountStaticType3?.auditFailedCountSql),
                      },
                    ]}
                  />
                </Col>
                <Col span={12}>
                  <RkPie
                    color={['#83BC99', '#ED7B7B']}
                    height={180}
                    radius={0.8}
                    innerRadius={0.6}
                    showPercentValue
                    innerTitle={'DM'}
                    label={{
                      type: 'inner',
                      content: '{value}',
                    }}
                    innerContent={auditCountStaticType4?.totalAuditCountSql || 0}
                    data={[
                      {
                        type: '成功次数',
                        value: renderNumber(auditCountStaticType4?.auditPassedCountSql),
                      },
                      {
                        type: '失败次数',
                        value: renderNumber(auditCountStaticType4?.auditFailedCountSql),
                      },
                    ]}
                  />
                </Col>
              </Row>
            </Card>
          </Col>
        )}

        <Col span={12}>
          <Card title="任务运行情况" bordered={false}>
            <RkPie
              color={['#87CBB9', '#FFB6D9', '#FF6969']}
              height={319}
              radius={0.8}
              data={[
                {
                  type: '运行中',
                  value: renderNumber(data?.auditTaskCountList?.[0]?.taskIngCount),
                },
                {
                  type: '运行结束',
                  value: renderNumber(data?.auditTaskCountList?.[0]?.taskEndCount),
                },
                {
                  type: '任务异常',
                  value: renderNumber(data?.auditTaskCountList?.[0]?.taskFailCount),
                },
              ]}
              label={{
                formatter: (v) => {
                  return `${v.type}:${v.value}`;
                },
              }}
            />
          </Card>
        </Col>

        {__ENABLE_DYNAMIC_AUDIT_ENABLED__ && (
          <Col span={24}>
            <Card title="采集SQL数量前10任务" bordered={false}>
              <RKColumn
                height={303}
                xField="taskName"
                yField="sqlCount"
                meta={{
                  taskName: {
                    alias: '任务名',
                  },
                  sqlCount: {
                    alias: 'SQL数量',
                  },
                }}
                data={
                  data?.auditCatchSqlList?.filter(
                    (i: API.DashboardCatchSql) => (i?.sqlCount || i?.sqlCount === 0) && i?.taskName,
                  ) || []
                }
                theme="custom-theme-cold"
              />
            </Card>
          </Col>
        )}

        <Col span={12}>
          <Card title="审核对象数量" bordered={false}>
            <RKColumn
              color="#A2C579"
              height={319}
              xField="dataSourceType"
              yField="dataSourceCount"
              xAxis={{
                label: {
                  autoHide: false,
                  autoRotate: true,
                },
              }}
              label={{
                formatter: (v) => {
                  return `${v.dataSourceCount}`;
                },
              }}
              meta={{
                dataSourceType: {
                  alias: '名称',
                },
                dataSourceCount: {
                  alias: '数量',
                },
              }}
              data={
                data?.dashboardDBCountList
                  ?.sort(
                    (a: API.DashboardDBCount, b: API.DashboardDBCount) =>
                      (b.dataSourceCount || 0) - (a.dataSourceCount || 0),
                  )
                  ?.map((i: API.DashboardDBCount) => {
                    return {
                      dataSourceType: DATASOURCE_TYPE.find(
                        (item) => item.value === String(i.dataSourceType),
                      )?.label,
                      dataSourceCount: i.dataSourceCount,
                    };
                  }) || []
              }
            />
          </Card>
        </Col>
      </Row>
    </PageContainer>
  );
};

export default Dashboard;
