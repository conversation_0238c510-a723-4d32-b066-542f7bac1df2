import RkPie from '@/components/Charts/Pie';
import { dashboardIndex } from '@/services/sql-guard/dashboard';
import { renderNumber } from '@/utils';
import { PageContainer } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Card, Col, Row } from 'antd';

const SqlAuditStatus = () => {
  const { data, loading } = useRequest<any>(dashboardIndex);

  const auditCountSqlType1 = data?.auditCountSqlList?.find(
    (i: API.DashboardAuditCountSql) => i.dbType === 1,
  );
  const auditCountSqlType2 = data?.auditCountSqlList?.find(
    (i: API.DashboardAuditCountSql) => i.dbType === 2,
  );
  const auditCountSqlType3 = data?.auditCountSqlList?.find(
    (i: API.DashboardAuditCountSql) => i.dbType === 3,
  );
  const auditCountSqlType4 = data?.auditCountSqlList?.find(
    (i: API.DashboardAuditCountSql) => i.dbType === 4,
  );
  const auditCountSqlType5 = data?.auditCountSqlList?.find(
    (i: API.DashboardAuditCountSql) => i.dbType === 5,
  );
  const auditCountSqlType6 = data?.auditCountSqlList?.find(
    (i: API.DashboardAuditCountSql) => i.dbType === 6,
  );

  return (
    <PageContainer header={{ title: '人工审核状态详情' }} loading={loading}>
      <Card bordered={false}>
        <Row gutter={12}>
          <Col span={8}>
            <RkPie
              color={['#83BC99', '#ED7B7B']}
              height={220}
              radius={0.8}
              innerRadius={0.6}
              showPercentValue
              innerTitle={'MySQL'}
              label={{
                type: 'inner',
                content: '{value}',
              }}
              innerContent={auditCountSqlType1?.totalAuditCountSql || 0}
              data={[
                {
                  type: '成功次数',
                  value: renderNumber(auditCountSqlType1?.auditPassedCountSql),
                },
                {
                  type: '失败次数',
                  value: renderNumber(auditCountSqlType1?.auditFailedCountSql),
                },
              ]}
            />
          </Col>
          <Col span={8}>
            <RkPie
              color={['#83BC99', '#ED7B7B']}
              height={220}
              radius={0.8}
              innerRadius={0.6}
              showPercentValue
              innerTitle={'Oracle'}
              label={{
                type: 'inner',
                content: '{value}',
              }}
              innerContent={auditCountSqlType2?.totalAuditCountSql || 0}
              data={[
                {
                  type: '成功次数',
                  value: renderNumber(auditCountSqlType2?.auditPassedCountSql),
                },
                {
                  type: '失败次数',
                  value: renderNumber(auditCountSqlType2?.auditFailedCountSql),
                },
              ]}
            />
          </Col>
          <Col span={8}>
            <RkPie
              color={['#83BC99', '#ED7B7B']}
              height={220}
              radius={0.8}
              innerRadius={0.6}
              showPercentValue
              innerTitle={'TDSQL'}
              label={{
                type: 'inner',
                content: '{value}',
              }}
              innerContent={auditCountSqlType3?.totalAuditCountSql || 0}
              data={[
                {
                  type: '成功次数',
                  value: renderNumber(auditCountSqlType3?.auditPassedCountSql),
                },
                {
                  type: '失败次数',
                  value: renderNumber(auditCountSqlType3?.auditFailedCountSql),
                },
              ]}
            />
          </Col>
          <Col span={8}>
            <RkPie
              color={['#83BC99', '#ED7B7B']}
              height={220}
              radius={0.8}
              innerRadius={0.6}
              showPercentValue
              innerTitle={'DM'}
              label={{
                type: 'inner',
                content: '{value}',
              }}
              innerContent={auditCountSqlType4?.totalAuditCountSql || 0}
              data={[
                {
                  type: '成功次数',
                  value: renderNumber(auditCountSqlType4?.auditPassedCountSql),
                },
                {
                  type: '失败次数',
                  value: renderNumber(auditCountSqlType4?.auditFailedCountSql),
                },
              ]}
            />
          </Col>
          <Col span={8}>
            <RkPie
              color={['#83BC99', '#ED7B7B']}
              height={220}
              radius={0.8}
              innerRadius={0.6}
              showPercentValue
              innerTitle={'OB(MySql兼容)'}
              innerTitleOffsetX={-8}
              innerTitleSize={'10px'}
              label={{
                type: 'inner',
                content: '{value}',
              }}
              innerContent={auditCountSqlType5?.totalAuditCountSql || 0}
              data={[
                {
                  type: '成功次数',
                  value: renderNumber(auditCountSqlType5?.auditPassedCountSql),
                },
                {
                  type: '失败次数',
                  value: renderNumber(auditCountSqlType5?.auditFailedCountSql),
                },
              ]}
            />
          </Col>
          <Col span={8}>
            <RkPie
              color={['#83BC99', '#ED7B7B']}
              height={220}
              radius={0.8}
              innerRadius={0.6}
              showPercentValue
              innerTitle={'OB(Oracle兼容)'}
              innerTitleOffsetX={-10}
              innerTitleSize={'10px'}
              label={{
                type: 'inner',
                content: '{value}',
              }}
              innerContent={auditCountSqlType6?.totalAuditCountSql || 0}
              data={[
                {
                  type: '成功次数',
                  value: renderNumber(auditCountSqlType6?.auditPassedCountSql),
                },
                {
                  type: '失败次数',
                  value: renderNumber(auditCountSqlType6?.auditFailedCountSql),
                },
              ]}
            />
          </Col>
        </Row>
      </Card>
    </PageContainer>
  );
};

export default SqlAuditStatus;
