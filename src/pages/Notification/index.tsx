import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { useRef } from 'react';

import { myPageSystemNotification, myRead } from '@/services/sql-guard/notification';
import { defaultTableConfig } from '@/utils/setting';
import { useRequest } from '@umijs/max';
import { Badge, Button, Space, message } from 'antd';

const Notification = () => {
  const actionRef = useRef<ActionType | undefined>();
  const [messageApi, contextHolder] = message.useMessage();

  // 成功回调
  const handleSuccess = () => {
    messageApi.success('操作成功！');
    actionRef.current?.reloadAndRest?.();
  };

  // 标记已读
  const { run } = useRequest((ids) => myRead({ ids }), {
    manual: true,
    onSuccess: (res) => {
      if (res?.code === 200) {
        handleSuccess();
      }
    },
    formatResult: (res) => res,
  });

  // 表格
  const columns: ProColumns<API.SystemNotificationVO>[] = [
    {
      title: '标题',
      dataIndex: 'title',
      render(dom, entity) {
        if (entity?.isRead) {
          return dom;
        }
        return <Badge status="error" text={dom} />;
      },
    },
    {
      title: '内容',
      dataIndex: 'messageContent',
      render(dom) {
        return <pre style={{ marginBottom: 0 }}>{dom}</pre>;
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
    },
    {
      title: '操作',
      fixed: 'right',
      width: 100,
      key: 'option',
      valueType: 'option',
      align: 'center',

      render: (_, record) => {
        const { isRead } = record;
        return (
          <Space>
            {!isRead && (
              <Button
                type="link"
                onClick={() => {
                  run([record.id]);
                }}
              >
                标记已读
              </Button>
            )}
          </Space>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      {contextHolder}
      <ProTable<API.SystemNotificationVO>
        {...defaultTableConfig}
        search={false}
        actionRef={actionRef}
        columns={columns}
        headerTitle="消息列表"
        request={async (params) => {
          const { current, pageSize } = params;
          const page = {
            page: current,
            size: pageSize,
          };
          const msg = await myPageSystemNotification({ page });
          return {
            ...msg?.data,
            data: msg?.data?.data || [],
            success: true,
            total: Number(msg?.data?.total) || 0,
          };
        }}
        polling={5000}
      />
    </PageContainer>
  );
};

export default Notification;
