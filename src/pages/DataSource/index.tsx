import { DATASOURCE_TYPE, RULE_AUDIT_MODE } from '@/enums';
import {
  deleteDataSource,
  listDataSource,
  testConnectionById,
} from '@/services/sql-guard/datasource';

import { option2enum, queryPagingTable } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { Access, history, useAccess, useRequest } from '@umijs/max';
import { Button, Modal, Space, Tag, message } from 'antd';
import { useRef, useState } from 'react';

const DataSource: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const [pagination, setPagination] = useState({ current: 1, pageSize: 10 });
  const {
    canAddDataSource = false,
    canUpdateDataSource = false,
    canDeleteDataSource = false,
  } = useAccess();

  const { run: deleteRecord } = useRequest((id) => deleteDataSource(id), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success(res.message);
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });
  const handleDelete = async (row: API.DataSourceResponse) => {
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除“${row.sourceName!}”吗?`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord({ id: Number(row.sourceId) });
      },
    });
  };
  const { run: linkRecord } = useRequest((id) => testConnectionById(id), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success(res.data);
      tableRef.current?.reload?.();
    },
    formatResult: (res) => res,
  });
  const handleLink = async (row: API.DataSourceResponse) => {
    linkRecord({ id: Number(row.sourceId) });
    // Modal.confirm({
    //   title: '确认连接',
    //   content: `您确定要连接数据库“${row.sourceName!}”，测试其连接是否正常吗?`,
    //   okText: '确认',
    //   cancelText: '取消',
    //   onOk: async () => {
    //     linkRecord({ id: Number(row.sourceId) });
    //   },
    // });
  };

  const onEdit = (record: API.DataSourceResponse) => {
    history.push(`/resource/datasource/edit/${record.sourceId}`);
  };

  const columns: ProColumns<API.DataSourceResponse>[] = [
    {
      title: '序号',
      render: (text, record, index) =>
        `${(pagination.current - 1) * pagination.pageSize + index + 1}`,
      width: 70,
      hideInSearch: true,
    },
    {
      title: '对象名称',
      dataIndex: 'sourceName',
      width: 170,
      ellipsis: true,
      copyable: true,
      render: (dom, record) => {
        return (
          <a
            className="rk-a-span"
            onClick={() => history.push(`/resource/datasource/detail/${record.sourceId}`)}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: '对象组',
      dataIndex: 'resourceGroupName',
      width: 150,
      ellipsis: true,
      hideInSearch: true,
      renderText: (text) => {
        return (
          <a
            className="rk-a-span"
            onClick={() =>
              history.push(`/resource/resource-group/list?current=1&pageSize=10&groupName=${text}`)
            }
          >
            {text}
          </a>
        );
      },
    },
    {
      title: '审核类型',
      dataIndex: 'auditMode',
      valueType: 'select',
      width: 100,
      ellipsis: true,
      initialValue: String(RULE_AUDIT_MODE[0]?.value),
      valueEnum: option2enum(RULE_AUDIT_MODE),
      fieldProps: (form) => ({
        allowClear: false,
        showSearch: true,
        filterOption: true,
        optionFilterProp: 'label',
        onChange: (val) => {
          form?.setFieldValue(
            'dbType',
            String(__FEATURE_TOGGLE__.defaultObjectTypeByAudit?.[Number(val)]),
          );
        },
      }),
    },
    {
      title: '对象类型',
      dataIndex: 'dbType',
      valueType: 'select',
      width: 200,
      ellipsis: true,
      dependencies: ['auditMode'],
      initialValue: String(
        __FEATURE_TOGGLE__.defaultObjectTypeByAudit?.[RULE_AUDIT_MODE[0]?.value],
      ),
      fieldProps: (form) => {
        const auditMode = Number(form?.getFieldValue?.('auditMode'));
        let filteredOptions: typeof DATASOURCE_TYPE = [];

        if (auditMode === 3) {
          filteredOptions = DATASOURCE_TYPE.filter((item) => {
            const itemValue = Number(item.value);
            return itemValue >= 301 && itemValue <= 308;
          });
        } else {
          filteredOptions = DATASOURCE_TYPE.filter((item) => {
            const itemValue = Number(item.value);
            return itemValue >= 1 && itemValue <= 6;
          });
        }
        return {
          allowClear: false,
          showSearch: true,
          filterOption: true,
          optionFilterProp: 'label',
          options: filteredOptions,
          placeholder: '请选择对象类型',
        };
      },
      renderText: (text, record) => {
        const obj = DATASOURCE_TYPE.find((item) => Number(item.value) === record.dbType);
        return <Tag color={obj?.color}>{obj?.label}</Tag>;
      },
    },
    {
      title: '对象IP',
      dataIndex: 'sourceIp',
      width: 150,
      ellipsis: true,
      copyable: true,
    },
    {
      title: '审核策略',
      dataIndex: 'ruleTemplateName',
      width: 150,
      ellipsis: true,
      hideInSearch: true,
      renderText: (text, record) => {
        return (
          <a
            className="rk-a-span"
            onClick={() =>
              history.push(`/basic/rules-template/detail/${record.ruleTemplateId}/${record.dbType}`)
            }
          >
            {text}
          </a>
        );
      },
    },
    // {
    //   title: '主机端口',
    //   dataIndex: 'hostPort',
    //   width: 150,
    //   ellipsis: true,
    //   search: false,
    // },
    // {
    //   title: '主机账号',
    //   dataIndex: 'hostAccount',
    //   width: 150,
    //   ellipsis: true,
    //   search: false,
    // },
    // {
    //   title: '主机密码',
    //   dataIndex: 'hostPassword',
    //   width: 150,
    //   ellipsis: true,
    //   search: false,
    // },
    {
      title: '操作',
      width: 150,
      key: 'option',
      fixed: 'right',
      valueType: 'option',
      align: 'center',
      render: (text, record) => {
        return (
          <Space>
            <a key="link" onClick={() => handleLink(record)}>
              测试连接
            </a>
            <Access key="edit" accessible={canUpdateDataSource}>
              <a onClick={() => onEdit(record)}>编辑</a>
            </Access>
            <Access key="del" accessible={canDeleteDataSource}>
              <a onClick={() => handleDelete(record)}>删除</a>
            </Access>
          </Space>
        );
      },
    },
  ];
  return (
    <PageContainer header={{ title: false }}>
      <ProTable<API.DataSourceResponse>
        {...defaultTableConfig}
        scroll={{ x: '100%' }}
        rowKey="sourceId"
        actionRef={tableRef}
        search={{
          labelWidth: 100,
          defaultCollapsed: false,
          filterType: 'query',
        }}
        columns={columns}
        headerTitle="目标对象列表"
        toolbar={{
          actions: [
            <Access key="add" accessible={canAddDataSource}>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  history.push('/resource/datasource/add');
                }}
              >
                新建对象
              </Button>
            </Access>,
          ],
        }}
        request={async (params) => {
          const res = await queryPagingTable<API.listDataSourceParams>(params, listDataSource);
          setPagination({ current: params.current || 1, pageSize: params.pageSize || 10 });
          return res;
        }}
      />
    </PageContainer>
  );
};

export default DataSource;
