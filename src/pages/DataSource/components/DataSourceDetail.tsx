import RKCol from '@/components/RKCol';
import { DATASOURCE_TYPE, RULE_AUDIT_MODE } from '@/enums';
import WithRouteEditing, { WithRouteEditingProps } from '@/hoc/withRouteEditing';
import { useListAuthGroup } from '@/hooks/useResourceGroupList';
import { useRuleTemplateList } from '@/hooks/useRuleTemplateList';
import {
  addDataSource,
  detailDataSource,
  testConnectionByInfo,
  updateDataSource,
} from '@/services/sql-guard/datasource';

import { onSuccessAndGoBack } from '@/utils';
import { requiredRule } from '@/utils/setting';
import {
  FooterToolbar,
  PageContainer,
  ProForm,
  ProFormDependency,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { useLocation, useRequest } from '@umijs/max';
import { But<PERSON>, <PERSON>, message } from 'antd';
import dayjs from 'dayjs';
import { useRef, useState } from 'react';

const DataSourceDetail: React.FC<WithRouteEditingProps> = ({ id, isEditPage }) => {
  const formRef = useRef<ProFormInstance>();
  const { pathname } = useLocation();
  const isDetailPage = pathname.includes('detail'); // 判断是否在详情页
  const { ruleTemplateList, ruleTemplateLoading, runRuleTemplate } = useRuleTemplateList();
  const { resourceGroupList, resourceGroupLoading } = useListAuthGroup();
  const [btnDisabled, setBtnDisabled] = useState<boolean>(true);
  const [updateTime, setUpdateTime] = useState<string>('');

  const { run: add, loading: addLoading } = useRequest((value) => addDataSource(value), {
    manual: true,
    onSuccess: onSuccessAndGoBack,
    formatResult: (res) => res,
  });
  const { run: linkTest, loading: linkLoading } = useRequest(
    (value) => testConnectionByInfo(value),
    {
      manual: true,
      onSuccess: (res) => {
        if (res.code !== 200) return;
        message.success('测试成功!');
        setBtnDisabled(false);
      },
      formatResult: (res) => res,
    },
  );
  const { run: edit, loading: editLoading } = useRequest((value) => updateDataSource(value), {
    manual: true,
    onSuccess: onSuccessAndGoBack,
    formatResult: (res) => res,
  });

  const getFilteredDataSourceTypes = (auditMode?: number): typeof DATASOURCE_TYPE => {
    if (!auditMode) return [];
    if (auditMode === 3) {
      return DATASOURCE_TYPE.filter((item) =>
        ['301', '302', '303', '304', '305', '306', '307', '308'].includes(item.value),
      );
    }
    return DATASOURCE_TYPE.filter((item) => ['1', '2', '3', '4', '5', '6'].includes(item.value));
  };

  return (
    <PageContainer
      header={{
        title:
          isDetailPage && updateTime
            ? '更新时间：' + dayjs(updateTime).format('YYYY-MM-DD HH:mm:ss')
            : false,
      }}
      className="detail-container"
    >
      <ProForm
        disabled={isDetailPage}
        formRef={formRef}
        submitter={
          isDetailPage
            ? false
            : {
                searchConfig: {
                  submitText: '保存',
                  resetText: '取消',
                },
                onReset: () => {
                  history.go(-1);
                },
                render: (props, doms) => {
                  return (
                    <FooterToolbar>
                      {doms?.[0]}
                      <ProFormDependency name={['auditMode']}>
                        {({ auditMode }) => (
                          <Button
                            type="primary"
                            key="linkTest"
                            onClick={async () => {
                              const formData =
                                (await formRef.current?.validateFieldsReturnFormatValue?.()) || {};
                              linkTest({ ...formData });
                            }}
                            loading={linkLoading}
                          >
                            {auditMode === 3 ? '测试主机连接' : '测试数据库连接'}
                          </Button>
                        )}
                      </ProFormDependency>
                      {doms?.[1]}
                    </FooterToolbar>
                  );
                },
                submitButtonProps: {
                  loading: addLoading || editLoading,
                  disabled: btnDisabled,
                },
              }
        }
        onFinish={async (value) => {
          const msg = isEditPage
            ? await edit({ ...value, sourceId: Number(id) })
            : await add(value);
          const success = msg.code === 200;
          setBtnDisabled(true);
          return success;
        }}
        request={async () => {
          if (!isEditPage) return {};
          const res = await detailDataSource({ id });
          setUpdateTime(res.data?.updateTime || '');
          runRuleTemplate(res.data?.dbType, res.data?.auditMode);
          return res.data;
        }}
      >
        <div className="rk-none">
          <ProFormText name="sid" label="sid" placeholder="请输入" />
          <ProFormText name="sourceId" label="sourceId" placeholder="请输入" />
        </div>

        <Row gutter={24}>
          <RKCol>
            <ProFormSelect
              name="resourceGroupId"
              label="所属对象组"
              options={resourceGroupList}
              fieldProps={{
                showSearch: true,
                filterOption: true,
                optionFilterProp: 'label',
                loading: resourceGroupLoading,
                fieldNames: {
                  label: 'groupName',
                  value: 'groupId',
                },
              }}
              disabled={isEditPage}
              rules={[requiredRule]}
              placeholder="请选择"
              transform={(value, namePath) => {
                return { [namePath]: value && Number(value) };
              }}
            />
          </RKCol>
          <RKCol>
            <ProFormSelect
              name="auditMode"
              label="审核类型"
              initialValue={RULE_AUDIT_MODE[0]?.value}
              options={RULE_AUDIT_MODE}
              fieldProps={{
                showSearch: true,
                filterOption: true,
                optionFilterProp: 'label',
                onChange: () => {
                  formRef.current?.setFieldValue('dbType', undefined);
                  formRef.current?.setFieldValue('ruleTemplateId', undefined);
                },
              }}
              disabled={isEditPage}
              rules={[requiredRule]}
              placeholder="请选择"
            />
          </RKCol>
          <RKCol>
            <ProFormDependency name={['auditMode']}>
              {({ auditMode }) => (
                <ProFormSelect
                  name="dbType"
                  label="对象类型"
                  options={getFilteredDataSourceTypes(auditMode)}
                  initialValue={String(
                    __FEATURE_TOGGLE__.defaultObjectTypeByAudit?.[RULE_AUDIT_MODE[0]?.value],
                  )}
                  fieldProps={{
                    showSearch: true,
                    filterOption: true,
                    optionFilterProp: 'label',
                    onChange: (val) => {
                      formRef.current?.setFieldValue('ruleTemplateId', undefined);
                      runRuleTemplate(val, auditMode);
                    },
                  }}
                  disabled={isEditPage || !auditMode}
                  rules={[requiredRule]}
                  placeholder="请选择"
                  transform={(value, namePath) => {
                    return { [namePath]: value && Number(value) };
                  }}
                  convertValue={(value) => value?.toString()}
                />
              )}
            </ProFormDependency>
          </RKCol>
          <ProFormDependency name={['dbType']}>
            {({ dbType }) => {
              if (dbType) {
                const options = ruleTemplateList.filter((item) => item.dbType === dbType);
                return (
                  <RKCol>
                    <ProFormSelect
                      name="ruleTemplateId"
                      label="审核策略"
                      options={options}
                      disabled={isEditPage || ruleTemplateLoading || isDetailPage}
                      fieldProps={{
                        showSearch: true,
                        filterOption: true,
                        optionFilterProp: 'label',
                        loading: ruleTemplateLoading,
                        fieldNames: {
                          label: 'templateName',
                          value: 'id',
                        },
                      }}
                      rules={[requiredRule]}
                      placeholder="请选择"
                    />
                  </RKCol>
                );
              }
            }}
          </ProFormDependency>
          <RKCol>
            <ProFormText
              name="sourceName"
              label="对象名称"
              placeholder="请输入"
              rules={[requiredRule]}
              disabled={isEditPage}
            />
          </RKCol>
          <RKCol>
            <ProFormText
              name="sourceIp"
              label="对象IP"
              placeholder="请输入"
              rules={[requiredRule]}
            />
          </RKCol>
          <ProFormDependency name={['auditMode']}>
            {({ auditMode }) => {
              if (auditMode !== 3) {
                return (
                  <>
                    <RKCol>
                      <ProFormText
                        name="sourcePort"
                        label="数据库端口"
                        placeholder="请输入"
                        rules={[requiredRule]}
                      />
                    </RKCol>
                    <RKCol>
                      <ProFormText
                        name="username"
                        label="数据库用户名"
                        placeholder="请输入"
                        rules={[requiredRule]}
                      />
                    </RKCol>
                    <RKCol>
                      <ProFormText.Password
                        name="password"
                        label="数据库密码"
                        placeholder="请输入"
                        rules={[requiredRule]}
                      />
                    </RKCol>
                  </>
                );
              } else {
                return (
                  <>
                    <RKCol>
                      <ProFormText
                        name="hostPort"
                        label="主机端口"
                        placeholder="请输入"
                        rules={[requiredRule]}
                      />
                    </RKCol>
                    <RKCol>
                      <ProFormText
                        name="hostAccount"
                        label="主机账号"
                        placeholder="请输入"
                        rules={[requiredRule]}
                      />
                    </RKCol>
                    <RKCol>
                      <ProFormText.Password
                        name="hostPassword"
                        label="主机密码"
                        placeholder="请输入"
                        rules={[requiredRule]}
                      />
                    </RKCol>
                  </>
                );
              }
            }}
          </ProFormDependency>

          <ProFormDependency name={['dbType']}>
            {({ dbType }) => {
              if (dbType === 2) {
                return (
                  <RKCol>
                    <ProFormText
                      name="serviceName"
                      label="服务名称"
                      placeholder="请输入"
                      rules={[requiredRule]}
                    />
                  </RKCol>
                );
              }
              if (dbType === 5 || dbType === 6) {
                return (
                  <>
                    <RKCol>
                      <ProFormText
                        name="tenant"
                        label="租户"
                        placeholder="请输入"
                        rules={[requiredRule]}
                      />
                    </RKCol>
                    <RKCol>
                      <ProFormText
                        name="cluster"
                        label="集群"
                        placeholder="请输入"
                        rules={[requiredRule]}
                      />
                    </RKCol>
                  </>
                );
              }
            }}
          </ProFormDependency>
        </Row>
      </ProForm>
    </PageContainer>
  );
};

export default WithRouteEditing(DataSourceDetail);
