import { useAllRoleList } from '@/hooks/useAllRoleList';
import { useAllUserList } from '@/hooks/useAllUserList';
import {
  assignApprovalFlowToResourceGroup,
  getApprovalFlowByResourceGroupId,
} from '@/services/sql-guard/approvalflow';
import {
  assignUsersToGroup,
  deleteResourceGroup,
  listResourceGroup,
} from '@/services/sql-guard/resourcegroup';
import { listUserOfGroup } from '@/services/sql-guard/user'; // 新增：导入接口
import { queryPagingTable } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { Access, useAccess, useRequest } from '@umijs/max';
import { Button, Form, Modal, Select, Space, Tag, message } from 'antd';
import { useRef, useState } from 'react';
import ResourceGroupModalForm from './components/ResourceGroupModalForm';

const ResourceGroup: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const [modalVisit, setModalVisit] = useState<boolean>(false);
  const [initialValues, setInitialValues] = useState<API.ResourceGroupResponse>({});
  // 新增分配用户状态和当前对象组
  const [assignModalVisible, setAssignModalVisible] = useState<boolean>(false);
  const [selectedResourceGroup, setSelectedResourceGroup] = useState<API.ResourceGroupResponse>({});
  const [assignForm] = Form.useForm();
  // 新增审批流配置状态
  const [approvalModalVisible, setApprovalModalVisible] = useState<boolean>(false);
  const [approvalFlowList, setApprovalFlowList] = useState<API.ApprovalFlowItem[]>([]);
  const {
    canAddResourceGroup = false,
    canUpdateResourceGroup = false,
    canDeleteResourceGroup = false,
    canAssignUserToResourceGroup = false,
    canSetApprovalFlow = false,
  } = useAccess();

  const { userList, userListLoading } = useAllUserList();
  const { roleList } = useAllRoleList();

  const { run: deleteRecord } = useRequest((id) => deleteResourceGroup(id), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success(res.message);
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });

  const { run: checkListOfGroup, loading } = useRequest(
    (record: API.ResourceGroupResponse) => listUserOfGroup({ groupId: Number(record.groupId) }),
    {
      manual: true,
      onSuccess: (res) => {
        if (res.code !== 200) return;
        if (res.data) {
          setAssignModalVisible(true);
          assignForm.resetFields();
          const assignedIds = res.data.map((user: API.UserVO) => user.id);
          assignForm.setFieldsValue({ userIds: assignedIds });
        }
      },
      formatResult: (res) => res,
    },
  );

  // 修改审批流请求hook，查询审批流并处理空数据
  const { run: checkApprovalFlow, loading: approvalFlowLoading } = useRequest(
    (record: API.ResourceGroupResponse) =>
      getApprovalFlowByResourceGroupId({ resourceGroupId: Number(record.groupId) }),
    {
      manual: true,
      onSuccess: (res) => {
        if (res.code !== 200) return;
        let list = res.data;
        if (!list || list.length === 0) {
          list = [{ approvalOrder: 1, roleId: '' }];
        }
        setApprovalFlowList(list);
        setApprovalModalVisible(true);
      },
      formatResult: (res) => res,
    },
  );

  // 新增：添加角色到审批流
  const addRoleToApprovalFlow = (roleId: string) => {
    setApprovalFlowList([
      ...approvalFlowList,
      { approvalOrder: approvalFlowList.length + 1, roleId },
    ]);
  };

  // 新增：删除审批流节点（除第一条外）
  const removeApprovalFlow = (index: number) => {
    const newList = approvalFlowList
      .filter((_, i) => i !== index)
      .map((item, i) => ({
        ...item,
        approvalOrder: i + 1,
      }));
    setApprovalFlowList(newList);
  };

  // 新增：提交审批流配置请求
  const { run: submitApprovalFlow, loading: approvalLoading } = useRequest(
    () =>
      assignApprovalFlowToResourceGroup({
        resourceGroupId: Number(selectedResourceGroup.groupId!),
        approvalFlowList,
      }),
    {
      manual: true,
      onSuccess: (res) => {
        if (res.code !== 200) return;
        message.success('审批流配置成功');
        setApprovalModalVisible(false);
        tableRef.current?.reload();
      },
      formatResult: (res) => res,
    },
  );

  const { run: handleAssign, loading: assignLoading } = useRequest(
    (values) =>
      assignUsersToGroup({
        resourceGroupId: selectedResourceGroup.groupId!,
        userIds: values.userIds,
      }),
    {
      manual: true,
      onSuccess: (res) => {
        if (res.code !== 200) return;
        message.success('分配成功');
        setAssignModalVisible(false);
        tableRef.current?.reload();
      },
      formatResult: (res) => res,
    },
  );

  const handleDelete = async (row: API.ResourceGroupResponse) => {
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除“${row.groupName!}”吗?`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord({ id: Number(row.groupId) });
      },
    });
  };

  const onEdit = (record: API.ResourceGroupResponse) => {
    setModalVisit(true);
    setInitialValues(record);
  };

  const onAssign = async (record: API.ResourceGroupResponse) => {
    setSelectedResourceGroup(record);
    checkListOfGroup(record);
  };

  // 提交分配用户表单
  const handleAssignSubmit = async () => {
    try {
      const values = await assignForm.validateFields();
      handleAssign(values);
    } catch (error) {}
  };

  const columns: ProColumns<API.ResourceGroupResponse>[] = [
    {
      title: '对象组名称',
      dataIndex: 'groupName',
      fixed: 'left',
      width: 150,
      ellipsis: true,
      copyable: true,
    },
    {
      title: '对象组描述',
      dataIndex: 'groupDescription',
      width: 200,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      width: 120,
      valueType: 'dateTime',
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '操作',
      width: 200,
      key: 'option',
      fixed: 'right',
      valueType: 'option',
      align: 'center',
      render: (text, record) => {
        return (
          <Space>
            <Access key="edit" accessible={canUpdateResourceGroup}>
              <a onClick={() => onEdit(record)}>编辑</a>
            </Access>
            <Access key="assign" accessible={canAssignUserToResourceGroup}>
              <Button
                type="link"
                className="inner-table-link"
                onClick={() => onAssign(record)}
                loading={loading}
              >
                分配用户
              </Button>
            </Access>
            <Access key="assignAudit" accessible={canSetApprovalFlow}>
              {__ENABLE_DYNAMIC_AUDIT_ENABLED__ && (
                <Button
                  type="link"
                  className="inner-table-link"
                  onClick={() => {
                    setSelectedResourceGroup(record);
                    checkApprovalFlow(record);
                  }}
                  loading={approvalFlowLoading}
                >
                  审批流配置
                </Button>
              )}
            </Access>
            <Access key="del" accessible={canDeleteResourceGroup}>
              <a onClick={() => handleDelete(record)}>删除</a>
            </Access>
          </Space>
        );
      },
    },
  ];
  return (
    <PageContainer header={{ title: false }}>
      <ProTable<API.ResourceGroupResponse>
        {...defaultTableConfig}
        scroll={{ x: '100%' }}
        rowKey="groupId"
        actionRef={tableRef}
        search={{
          labelWidth: 100,
          defaultCollapsed: false,
          filterType: 'query',
        }}
        columns={columns}
        headerTitle="对象组列表"
        toolbar={{
          actions: [
            <Access key="add" accessible={canAddResourceGroup}>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  setModalVisit(true);
                  setInitialValues({});
                }}
              >
                新建对象组
              </Button>
            </Access>,
          ],
        }}
        request={async (params) =>
          queryPagingTable<API.listResourceGroupParams>(params, listResourceGroup)
        }
      />
      <ResourceGroupModalForm
        initialValues={initialValues}
        onOpenChange={(visit) => setModalVisit(visit)}
        open={modalVisit}
        onFinish={async () => {
          tableRef.current?.reload();
        }}
      />

      {/* 分配用户 Modal */}
      <Modal
        title={`分配用户 - ${selectedResourceGroup.groupName}`}
        open={assignModalVisible}
        onCancel={() => setAssignModalVisible(false)}
        onOk={handleAssignSubmit}
        confirmLoading={assignLoading}
        destroyOnClose
      >
        <Form form={assignForm} layout="vertical">
          <Form.Item
            name="userIds"
            label="选择用户"
            rules={[{ required: true, message: '请选择用户' }]}
          >
            <Select mode="multiple" placeholder="请选择用户" loading={userListLoading} allowClear>
              {userList.map((user: API.UserVO) => (
                <Select.Option key={user.id} value={user.id}>
                  {user.username}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      {/* 新增：审批流配置 Modal */}
      <Modal
        width="40%"
        title={`审批流配置 - ${selectedResourceGroup.groupName}`}
        open={approvalModalVisible}
        onCancel={() => setApprovalModalVisible(false)}
        onOk={submitApprovalFlow}
        confirmLoading={approvalLoading}
        destroyOnClose
      >
        <div>
          当前审批流:
          <div>
            {approvalFlowList.map((item, index) => (
              <span key={`${index}-${item.roleId}`}>
                {index > 0 && (
                  <span style={{ display: 'inline-block', margin: '0 10px 0 5px' }}>{'>'}</span>
                )}
                <Tag
                  color="green"
                  style={{ marginTop: 10 }}
                  closable={index !== 0}
                  onClose={(e) => {
                    e.preventDefault();
                    removeApprovalFlow(index);
                  }}
                >
                  {item.approvalOrder === 1 && item.roleId === ''
                    ? '提交人'
                    : roleList.find((role: API.RoleResponse) => role.id === item.roleId)?.name ||
                      '未知'}
                </Tag>
              </span>
            ))}
          </div>
          <div style={{ marginTop: 16 }}>
            选择角色加入审批流:
            <div>
              {roleList.map((role: API.RoleResponse) => (
                <Tag
                  color="processing"
                  key={role.id}
                  onClick={() => addRoleToApprovalFlow(role.id!)}
                  style={{ cursor: 'pointer', marginTop: 10 }}
                >
                  {role.name}
                </Tag>
              ))}
            </div>
          </div>
        </div>
      </Modal>
    </PageContainer>
  );
};

export default ResourceGroup;
