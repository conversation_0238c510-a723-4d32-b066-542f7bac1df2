import { addResourceGroup, updateResourceGroup } from '@/services/sql-guard/resourcegroup';
import { requiredRule } from '@/utils/setting';
import {
  ModalForm,
  ModalFormProps,
  ProFormInstance,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { message } from 'antd';
import React, { useRef } from 'react';

const ResourceGroupModalForm: React.FC<ModalFormProps> = ({
  initialValues,
  open,
  onOpenChange,
  onFinish,
}) => {
  const formRef = useRef<ProFormInstance>();
  const isEdit = initialValues?.groupId;

  return (
    <ModalForm
      width={475}
      title={isEdit ? '编辑' : '新增'}
      formRef={formRef}
      open={open}
      onOpenChange={onOpenChange}
      onFinish={async (value) => {
        const msg = isEdit ? await updateResourceGroup(value) : await addResourceGroup(value);
        const success = msg.code === 200;
        if (success) {
          message.success('操作成功!');
          onFinish?.(value);
        }
        return success;
      }}
      initialValues={initialValues}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        centered: true,
      }}
    >
      <div className="rk-none">
        <ProFormText name="groupId" label="groupId" placeholder="请输入" />
      </div>
      <ProFormText
        name="groupName"
        label="对象组名称"
        placeholder="请输入"
        rules={[requiredRule]}
      />

      <ProFormTextArea
        name="groupDescription"
        label="对象组描述"
        placeholder="请输入"
        rules={[requiredRule]}
        fieldProps={{
          autoSize: { minRows: 2, maxRows: 4 },
        }}
      />
    </ModalForm>
  );
};

export default ResourceGroupModalForm;
