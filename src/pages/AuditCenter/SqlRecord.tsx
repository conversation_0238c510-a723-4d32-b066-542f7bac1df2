import { useListAuthGroup } from '@/hooks/useResourceGroupList';
import { listDsByGroupIdAndAuditMode } from '@/services/sql-guard/datasource';
import { listSqlDetail, listSqlRecords } from '@/services/sql-guard/sqlRecord';
import { defaultTableConfig } from '@/utils/setting';
import { PageContainer, ProColumns, ProFormInstance, ProTable } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Modal, Space } from 'antd';
import { useRef, useState } from 'react';
import { formatData } from '../AuditSql';

const SqlRecord: React.FC = () => {
  const formRef = useRef<ProFormInstance>();
  const { resourceGroupList, resourceGroupLoading } = useListAuthGroup();
  const [currentPage, setCurrentPage] = useState(1);
  const [currPageSize, setCurrPageSize] = useState(10);

  // 详情弹窗相关状态
  const [detailModal, setDetailModal] = useState<{ visible: boolean; recordId?: string }>({
    visible: false,
  });
  const [detailData, setDetailData] = useState<{ list: any[]; total: number }>({
    list: [],
    total: 0,
  });
  const [detailLoading, setDetailLoading] = useState(false);
  const [detailPage, setDetailPage] = useState(1);
  const [detailPageSize, setDetailPageSize] = useState(10);

  // 数据源列表加载和状态管理
  const {
    run: runDataSource,
    data: dataSourceList = [],
    loading: dataSourceLoading,
  } = useRequest((value) => listDsByGroupIdAndAuditMode(value), {
    manual: true,
  });

  // 查询明细数据
  const fetchDetail = async (sqlSha256: string, pageNo: number, pageSize: number) => {
    setDetailLoading(true);
    try {
      const res = await listSqlDetail({ sqlSha256, pageNo, pageSize });
      setDetailData({ list: res?.data?.records || [], total: Number(res?.data?.total) || 0 });
    } finally {
      setDetailLoading(false);
    }
  };

  // 打开详情弹窗
  const openDetail = (sqlSha256: string) => {
    setDetailModal({ visible: true, recordId: sqlSha256 });
    setDetailPage(1);
    fetchDetail(sqlSha256, 1, detailPageSize);
  };

  // ProTable的列定义
  const columns: ProColumns<any>[] = [
    {
      title: '序号',
      dataIndex: 'index',
      width: 80,
      hideInSearch: true,
      render: (_, record, index) => {
        return (currentPage - 1) * currPageSize + index + 1;
      },
    },
    {
      title: '目标对象',
      dataIndex: 'sourceName',
      width: 120,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: 'SQL文本',
      dataIndex: 'sqlText',
      width: 300,
      ellipsis: true,
    },
    {
      title: '对象组',
      dataIndex: 'resourceGroupId',
      hideInTable: true,
      valueType: 'select',
      fieldProps: {
        options: resourceGroupList,
        loading: resourceGroupLoading,
        showSearch: true,
        filterOption: true,
        optionFilterProp: 'label',
        fieldNames: {
          label: 'groupName',
          value: 'groupId',
        },
        onChange: (val: number) => {
          formRef.current?.setFieldsValue({ sourceId: undefined });
          runDataSource({ groupId: val, auditMode: 1 });
        },
      },
    },
    {
      title: '目标对象',
      dataIndex: 'sourceId',
      hideInTable: true,
      valueType: 'select',
      fieldProps: {
        showSearch: true,
        filterOption: true,
        optionFilterProp: 'label',
        options: formatData(dataSourceList)?.map((item) => ({
          label: item.sourceName,
          value: item.sourceId,
          dbType: item.dbType,
        })),
        loading: dataSourceLoading,
        placeholder: '请选择目标对象',
        optionItemRender: (item: Record<string, any>) => {
          return (
            <Space>
              {item.label}
              {/* <Tag color={DATASOURCE_TYPE.find((it) => Number(it.value) === item.dbType)?.color}>
                {DATASOURCE_TYPE.find((it) => Number(it.value) === item.dbType)?.label}
              </Tag> */}
            </Space>
          );
        },
      },
    },
    {
      title: '使用次数',
      width: 180,
      dataIndex: 'usageCount',
      hideInSearch: true,
    },
    {
      title: '操作',
      width: 100,
      key: 'option',
      fixed: 'right',
      valueType: 'option',
      align: 'center',
      render: (_, record) => <a onClick={() => openDetail(record.sqlSha256)}>查看明细</a>,
    },
    // 搜索表单项
  ];

  // 明细表格列定义
  const detailColumns: ProColumns<any>[] = [
    {
      title: 'SQL语句',
      dataIndex: 'sqlStatement',
      ellipsis: true,
    },
    {
      title: '优化建议',
      dataIndex: 'optimizedSql',
      ellipsis: true,
    },
    {
      title: '捕获时间',
      dataIndex: 'updatedAt',
      valueType: 'dateTime',
      width: 190,
    },
  ];

  return (
    <PageContainer header={{ title: false }}>
      <ProTable
        {...defaultTableConfig}
        formRef={formRef}
        scroll={{ x: '100%' }}
        rowKey="sqlSha256"
        columns={columns}
        search={{
          defaultCollapsed: false,
          filterType: 'query',
        }}
        request={async (params) => {
          const { current, pageSize } = params;
          setCurrentPage(current as number);
          setCurrPageSize(pageSize as number);

          const { sourceId, sqlText } = params;

          const result = await listSqlRecords({
            sourceId: sourceId as number,
            sqlText: sqlText as string,
            pageNo: current,
            pageSize,
          });

          return {
            data: result.data?.records || [],
            total: Number(result.data?.total) || 0,
            success: result.code === 200,
          };
        }}
        pagination={{
          defaultPageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
        }}
      />

      <Modal
        title="SQL明细"
        open={detailModal.visible}
        onCancel={() => setDetailModal({ visible: false })}
        footer={null}
        width="65%"
      >
        <div style={{ minHeight: '20vh' }}>
          <ProTable
            {...defaultTableConfig}
            rowKey="sqlStatement"
            columns={detailColumns}
            dataSource={detailData.list}
            loading={detailLoading}
            search={false}
            options={false}
            pagination={{
              current: detailPage,
              pageSize: detailPageSize,
              total: detailData.total,
              showSizeChanger: true,
              onChange: (p, ps) => {
                setDetailPage(p);
                setDetailPageSize(ps);
                if (detailModal.recordId) fetchDetail(detailModal.recordId as string, p, ps);
              },
            }}
          />
        </div>
      </Modal>
    </PageContainer>
  );
};

export default SqlRecord;
