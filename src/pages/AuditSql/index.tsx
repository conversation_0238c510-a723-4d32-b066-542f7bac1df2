import BaseListContext from '@/components/Context/BaseListContext';
import { DATASOURCE_TYPE } from '@/enums';
import { useListAuthGroup } from '@/hooks/useResourceGroupList';
import {
  listDbNameOrSchemaBysourceId,
  listDsByGroupIdAndAuditMode,
} from '@/services/sql-guard/datasource';
import { auditSql, sqlFormat } from '@/services/sql-guard/sqlaudit';
import { createWorkOrder } from '@/services/sql-guard/workorder';
import { getRandomId } from '@/utils';
import { requiredRule } from '@/utils/setting';
import {
  PageContainer,
  ProCard,
  ProForm,
  ProFormDateTimeRangePicker,
  ProFormDependency,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { sql } from '@codemirror/lang-sql';
import ReactCodeMirror from '@uiw/react-codemirror';
import { Access, history, useAccess, useRequest } from '@umijs/max';
import { Button, Empty, Modal, Space, Tag, message } from 'antd';
import dayjs from 'dayjs'; // 新增：引入 dayjs
import { useRef, useState } from 'react';
import AuditResultTable from './components/AuditResultTable';
import styles from './index.less';
export const formatData = (data: Record<string, any>[] = []) => {
  return data.reduce((acc: Record<string, any>[], cur) => {
    const { sourceInfo, ...rest } = cur;
    // 预处理字符串，使其成为有效的 JSON 格式
    const processedString = sourceInfo
      .replace(/\{(\d+),/g, '{"sourceId":$1,"sourceName":"')
      .replace(/\}/g, '"}');
    const result = JSON.parse(processedString).map((it: Record<string, any>) => ({
      ...rest,
      ...it,
      value: it.sourceId!.toString(),
      // value: getRandomId(),
      label: it.sourceName,
    }));
    const arr = [...acc, ...result];
    return arr;
  }, []);
};
const AuditSql: React.FC = () => {
  const formRef = useRef<ProFormInstance>();
  const { resourceGroupList, resourceGroupLoading } = useListAuthGroup();
  const [codeValue, setCodeValue] = useState<string>('');
  const { canSubmitWorkOrder = false, canSqlApprove = false } = useAccess();

  const {
    run: runSource,
    data: sourceData = [],
    loading: sourceLoading,
  } = useRequest((value) => listDsByGroupIdAndAuditMode(value), {
    manual: true,
  });
  const {
    run: runInstance,
    data: instanceData = [],
    loading: instanceLoading,
  } = useRequest((value) => listDbNameOrSchemaBysourceId(value), {
    manual: true,
  });

  const { run: runCreate, loading: createLoading } = useRequest((value) => createWorkOrder(value), {
    manual: true,
    onSuccess: (res) => {
      if (res?.code !== 200) return;
      message.success(res.message);
      formRef.current?.resetFields?.();
      setCodeValue('');
      setTimeout(() => {
        history.push('/sql/work-order/list');
      }, 1500);
    },
    formatResult: (res) => res,
  });

  const instanceOptions = instanceData?.map((item) => ({
    label: item,
    value: `${item}-${getRandomId()}`,
  }));

  const handleSqlFormat = async () => {
    if (!codeValue) return message.error('请输入SQL语句');
    // try {
    //   const newValue = format(codeValue, { language: 'sql', denseOperators: false });
    //   setCodeValue(newValue);
    // } catch (err: any) {
    //   console.log('err', err);
    //   message.error(err.message || 'sql语法错误');
    //   return;
    // }
    const msg = await sqlFormat({ sql: codeValue });
    const success = msg.code === 200;
    if (!success) return message.error(msg.message || '格式化失败');
    // message.success(msg.message);
    setCodeValue(msg.data!.sql);
  };
  const [tab, setTab] = useState<string>();
  const {
    data = [],
    run: runAudit,
    loading: auditLoading,
  } = useRequest((value) => auditSql(value), {
    manual: true,
    onSuccess: (res) => {
      setTab(res!.at(0)!.id!);
    },
  });

  const auditPassed = data?.length > 0 && data.every((item) => item?.auditStatus === 1);

  const handleAudit = async () => {
    const formData = formRef.current?.getFieldsFormatValue?.();
    const { sourceId, dbNameOrSchema } = formData;
    if (!sourceId || !dbNameOrSchema || !codeValue) {
      return message.error('请将sql语句、对象组、数据库等信息填写完整！');
    }
    const values = `${codeValue}\n`
      .split(/;\n/)
      .map((item) => item.replace(/\n/g, ' '))
      .map((item) => item.trim())
      .filter((item) => item.length > 0);
    if (values.length > 10) {
      message.error('一次最多支持10条SQL语句');
    } else {
      runAudit({ sql: values, sourceId, dbNameOrSchema });
    }
  };
  const items = data?.map((item, index) => ({
    ...item,
    label: `审核结果${index + 1}`,
    key: item.id!,
    children: <AuditResultTable dataSource={item} />,
  }));
  return (
    <PageContainer header={{ title: false }}>
      <ProCard title="SQL提交" headerBordered>
        <ProCard colSpan="60%">
          <ReactCodeMirror
            value={codeValue}
            onChange={(value) => {
              setCodeValue(value);
            }}
            autoFocus
            maxWidth="100%"
            height="465px"
            placeholder="请输入SQL语句,多条语句以分号分隔，最多支持10条语句"
            className={styles.codeMirror}
            basicSetup={{
              foldGutter: false,
            }}
            extensions={[sql()]}
          />
        </ProCard>
        <ProCard colSpan="40%">
          <ProForm
            formRef={formRef}
            submitter={{
              searchConfig: {
                submitText: '提交工单',
              },
              resetButtonProps: {
                style: {
                  display: 'none',
                },
              },
              render: () => {
                return (
                  <Space>
                    <Button
                      type="primary"
                      style={{ backgroundColor: '#FFA500' }}
                      onClick={() => handleSqlFormat()}
                    >
                      SQL格式化
                    </Button>
                    <Access accessible={canSqlApprove}>
                      <Button onClick={() => handleAudit()} color="default" danger type="primary">
                        人工审核
                      </Button>
                    </Access>
                    <Access accessible={canSubmitWorkOrder}>
                      <Button
                        disabled={
                          //data数组必须要有值且必须每项的auditStatus都为1
                          data?.length === 0 || !auditPassed
                        }
                        loading={createLoading}
                        onClick={async () => {
                          try {
                            await formRef.current?.validateFields();
                          } catch (error) {
                            return;
                          }
                          const formData = formRef.current?.getFieldsFormatValue?.();
                          const values = `${codeValue}\n`
                            .split(/;\n/)
                            .map((item) => item.replace(/\n/g, ' '))
                            .map((item) => item.trim())
                            .filter((item) => item.length > 0);
                          const form = {
                            workOrderName: formData?.workOrderName,
                            executionTimeRange: formData?.executionTimeRange,
                            resourceGroupId: formData?.groupId,
                            dataSourceId: formData?.sourceId,
                            dbInstanceName: formData?.dbNameOrSchema,
                            sqlContentList: values,
                          };
                          Modal.confirm({
                            title: '确认创建',
                            content: `您确定要创建工单吗?`,
                            okText: '确认',
                            cancelText: '取消',
                            onOk: () => {
                              runCreate(form);
                            },
                          });
                        }}
                        type="primary"
                      >
                        创建工单
                      </Button>
                    </Access>
                    {/* {doms?.[1]} */}
                  </Space>
                );
              },
            }}
            onValuesChange={(val) => {
              if (val.groupId) {
                runSource({ groupId: Number(val.groupId), auditMode: 1 });
              }
              if (val.sourceId) {
                runInstance({ sourceId: Number(val.sourceId) });
              }
            }}
          >
            <div className="rk-none">
              <ProFormText name="sourceType" label="数据库类型" placeholder="请输入" />
            </div>
            <ProFormSelect
              name="groupId"
              label="对象组"
              options={resourceGroupList}
              fieldProps={{
                showSearch: true,
                filterOption: true,
                optionFilterProp: 'label',
                loading: resourceGroupLoading,
                disabled: resourceGroupLoading, // 新增：加载时禁用输入
                fieldNames: {
                  label: 'groupName',
                  value: 'groupId',
                },
                onChange: () => {
                  formRef.current?.setFieldsValue({
                    sourceId: undefined,
                    dbNameOrSchema: undefined,
                  });
                },
              }}
              rules={[requiredRule]}
              placeholder="请选择"
            />
            <ProFormSelect
              name="sourceId"
              label="目标对象"
              tooltip="请先选择对象组"
              options={formatData(sourceData)}
              fieldProps={{
                showSearch: true,
                filterOption: true,
                optionFilterProp: 'label',
                loading: sourceLoading ?? true,
                disabled: sourceLoading, // 新增：加载时禁用输入
                onChange: (_, option: Record<string, any>) => {
                  formRef.current?.setFieldsValue({
                    dbNameOrSchema: undefined,
                    sourceType: option?.dbType,
                  });
                },
                optionItemRender: (item: Record<string, any>) => {
                  return (
                    <Space>
                      {item.sourceName}
                      <Tag
                        color={
                          DATASOURCE_TYPE.find((it) => Number(it.value) === item.dbType)?.color
                        }
                      >
                        {item.dbTypeName}
                      </Tag>
                    </Space>
                  );
                },
              }}
              rules={[requiredRule]}
              placeholder="请选择"
              transform={(value, namePath) => {
                return { [namePath]: value && Number(value) };
              }}
            />
            <ProFormDependency name={['sourceType']}>
              {({ sourceType }) => {
                const label = [2, 4, 6].includes(sourceType) ? 'schema' : '数据库';
                return (
                  <ProFormSelect
                    name="dbNameOrSchema"
                    label={label}
                    options={instanceOptions}
                    tooltip="请先选择对象组和数据库"
                    fieldProps={{
                      showSearch: true,
                      filterOption: true,
                      optionFilterProp: 'label',
                      loading: instanceLoading ?? true,
                      disabled: instanceLoading, // 新增：加载时禁用输入
                    }}
                    rules={[requiredRule]}
                    placeholder="请选择"
                    transform={(value, namePath) => {
                      return { [namePath]: value && value.split('-')[0] };
                    }}
                  />
                );
              }}
            </ProFormDependency>
            <ProFormText
              rules={[
                {
                  required: auditPassed,
                  message: '工单名称不能为空',
                },
              ]}
              name="workOrderName"
              label="工单名称"
              placeholder="请输入"
            />
            <ProFormDateTimeRangePicker
              name="executionTimeRange"
              label="可执行时间范围"
              fieldProps={{ format: 'YYYY-MM-DD HH:mm', style: { width: '100%' } }} // 修改：设置宽度为100%
              rules={[
                {
                  required: auditPassed,
                  message: '可执行时间范围不能为空',
                },
              ]}
              transform={(value) => ({
                executionTimeRange: value
                  ? `${dayjs(value[0]).format('YYYY-MM-DD HH:mm')} -> ${dayjs(value[1]).format(
                      'YYYY-MM-DD HH:mm',
                    )}`
                  : undefined,
              })}
              placeholder={['开始时间', '结束时间']}
            />
          </ProForm>
        </ProCard>
      </ProCard>
      <BaseListContext.Provider value={{ sourceList: formatData(sourceData), sourceLoading }}>
        <ProCard
          bordered
          headerBordered
          ghost
          style={{ marginBlockStart: 24 }}
          loading={auditLoading}
        >
          {data?.length > 0 ? (
            <>
              <ProCard
                tabs={{
                  activeKey: tab,
                  items: items,
                  onChange: (key) => {
                    setTab(key);
                  },
                }}
                bordered
                headerBordered
              />
            </>
          ) : (
            <ProCard title="审核结果">
              <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="暂无审核结果" />
            </ProCard>
          )}
        </ProCard>
      </BaseListContext.Provider>
    </PageContainer>
  );
};

export default AuditSql;
