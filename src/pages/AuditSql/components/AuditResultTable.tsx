import BaseListContext from '@/components/Context/BaseListContext';
import { WARN_LEVEL } from '@/enums';
import { listViolationsByAuditResultId } from '@/services/sql-guard/sqlaudit';
import { option2enum } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { ActionType, ProColumns, ProDescriptions, ProTable } from '@ant-design/pro-components';
import { Tag } from 'antd';
import React, { useContext, useRef } from 'react';

const AuditResultTable: React.FC<{
  dataSource?: API.SqlAuditResponse;
}> = ({ dataSource = {} }) => {
  const tableRef = useRef<ActionType>();
  const { sourceList, sourceLoading } = useContext(BaseListContext);
  const desColumns = [
    {
      dataIndex: 'auditInfo',
      title: '审核状态',
      renderText: (text: string, record: Record<string, any>) => (
        <Tag color={record.auditStatus === 1 ? 'success' : 'error'}>{text}</Tag>
      ),
    },
    {
      dataIndex: 'sourceId',
      title: '数据库',
      valueType: 'select',
      fieldProps: {
        options: sourceList,
        loading: sourceLoading,
      },
    },
    {
      dataIndex: 'sqlStatement',
      title: '执行SQL语句',
    },
    {
      dataIndex: 'sqlKind',
      title: 'SQL种类',
    },
    {
      dataIndex: 'optimizedSql',
      title: '优化建议',
    },
  ];
  const columns: ProColumns<API.AuditParameterResponse>[] = [
    {
      title: '规则',
      dataIndex: 'violatedRuleName',
      // width: 150,
      fixed: 'left',
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '告警等级',
      dataIndex: 'alertLevel',
      width: 80,
      ellipsis: true,
      hideInSearch: true,
      valueType: 'select',
      valueEnum: option2enum(WARN_LEVEL),
    },
    {
      title: '类型',
      dataIndex: 'ruleTypeName',
      width: 100,
      ellipsis: true,
      hideInSearch: true,
    },
    // {
    //   title: 'SQL类型',
    //   dataIndex: 'applicableSceneName',
    //   width: 100,
    //   ellipsis: true,
    //   hideInSearch: true,
    // },
    {
      title: '审核目的',
      dataIndex: 'auditPurpose',
      width: 100,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '脚本',
      dataIndex: 'ruleResourceName',
      width: 80,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '说明',
      dataIndex: 'errorMessage',
      width: 150,
      ellipsis: true,
      hideInSearch: true,
    },
  ];
  return (
    <>
      <ProDescriptions
        column={2}
        bordered
        dataSource={dataSource}
        labelStyle={{
          width: 150,
        }}
        columns={desColumns}
      />
      {dataSource.auditStatus !== 1 && (
        <ProTable<API.AuditResultViolationsResponse>
          {...defaultTableConfig}
          actionRef={tableRef}
          // scroll={{ x: '100%' }}
          ghost
          columns={columns}
          headerTitle={
            <span style={{ fontWeight: 'bold', fontSize: '1.1em', color: 'var(--primary-color)' }}>
              违反规则详细列表
            </span>
          }
          request={async ({ current: pageNum, pageSize }) => {
            const msg = await listViolationsByAuditResultId({
              auditResultId: dataSource.id!,
              pageNum,
              pageSize,
            });
            return {
              success: true,
              data: msg.data?.records || [],
              total: Number(msg.data?.total) || 0,
            };
          }}
        />
      )}
    </>
  );
};

export default AuditResultTable;
