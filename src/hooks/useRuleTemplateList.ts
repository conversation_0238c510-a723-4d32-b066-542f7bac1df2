import { listRuleTemplate } from '@/services/sql-guard/ruleTemplate';
import { useRequest } from '@umijs/max';

/**
 * @description 所有审核策略
 * @returns 审核策略列表
 * @returns 加载状态
 */
export function useRuleTemplateList() {
  const {
    data = {},
    loading,
    run: runRuleTemplate,
  } = useRequest(
    (dbType = 1, auditMode = 1) =>
      //@ts-ignore
      listRuleTemplate({ pageNum: 1, pageSize: 1000000, dbType, auditMode }),
    {
      manual: true,
    },
  );
  return {
    runRuleTemplate,
    ruleTemplateList: data?.records || [],
    ruleTemplateLoading: loading,
  };
}
