import { listAuthGroup, listIdAndName } from '@/services/sql-guard/resourcegroup';
import { useRequest } from '@umijs/max';

/**
 *
 * @description 所有对象组
 * @returns 对象组列表
 * @returns 加载状态
 */
export function useResourceGroupList() {
  const { data = {}, loading } = useRequest(() => listIdAndName());
  return {
    resourceGroupList: data?.records || [],
    resourceGroupLoading: loading,
  };
}

/**
 *
 * @description 所有对象组(带登录权限)
 * @returns 对象组列表
 * @returns 加载状态
 */
export function useListAuthGroup() {
  const { data = [], loading } = useRequest(() => listAuthGroup());
  return {
    resourceGroupList: data,
    resourceGroupLoading: loading,
  };
}
