import { listDataSource } from '@/services/sql-guard/datasource';
import { useRequest } from '@umijs/max';

/**
 * @description  所有数据库
 * @returns 数据库列表
 * @returns 加载状态
 */
export function useDataSourceList() {
  const {
    data = {},
    loading,
    run: runDataSource,
  } = useRequest(
    (dbType = 1, auditMode = 1) =>
      listDataSource({ pageNum: 1, pageSize: 1000000, dbType, auditMode }),
    {
      manual: true,
    },
  );
  return {
    runDataSource,
    dataSourceList: data?.records || [],
    dataSourceLoading: loading,
  };
}
