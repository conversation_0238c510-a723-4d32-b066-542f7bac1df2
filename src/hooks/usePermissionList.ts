import { listPermission } from '@/services/sql-guard/permission';
import { useRequest } from '@umijs/max';

/**
 * @description 所有权限列表
 * @returns 所有权限列表
 * @returns 加载状态
 */
export function usePermissionList() {
  const { data = {}, loading } = useRequest(() =>
    listPermission({ pageNum: 1, pageSize: 1000000 }),
  );
  return {
    permissionList: data?.records || [],
    permissionListLoading: loading,
  };
}
