import { AvatarDropdown, Footer, Notification, RKAvatar } from '@/components';
import { LinkOutlined } from '@ant-design/icons';
import type { Settings as LayoutSettings } from '@ant-design/pro-components';
import type { RunTimeLayoutConfig } from '@umijs/max';
import { Link, history } from '@umijs/max';
import defaultSettings, { isMicroservice } from '../config/defaultSettings';
import UnAccessible from './pages/403';
import { errorConfig } from './requestErrorConfig';

const isDev = process.env.NODE_ENV === 'development';
const loginPath = '/user/login';

// 注入 COMMIT_HASH、APP_MODE
window.COMMIT_HASH = process.env.COMMIT_HASH;
window.APP_MODE = defaultSettings.APP_MODE;

// 主题css 变量注入

import { ConfigProvider, message } from 'antd';
import { currentUser } from './services/sql-guard/user';
ConfigProvider.config({
  theme: {
    primaryColor: '#13c2c2',
    successColor: '#52c41a',
    errorColor: '#ff4d4f',
    warningColor: '#faad14',
  },
});

/**
 * @see  https://umijs.org/zh-CN/plugins/plugin-initial-state
 * */

type SettingProps = Partial<LayoutSettings>;
export async function getInitialState(): Promise<{
  settings?: SettingProps;
  currentUser?: API.UserVO;
  loading?: boolean;
}> {
  const fetchUserInfo = async () => {
    try {
      const msg = await currentUser();
      return msg?.data;
    } catch (error) {
      console.log('🚗 🚗 🚗 ~ fetchUserInfo ~ error:', error);
    }
    return undefined;
  };

  // 如果不是登录页面，执行
  const { location } = history;
  const token = localStorage.getItem(defaultSettings.TOKEN_KEY);

  if (location.pathname.includes(loginPath) || (location.pathname === '/' && !token)) {
    return {
      settings: defaultSettings as SettingProps,
    };
  }
  const userInfo = await fetchUserInfo();
  return {
    currentUser: userInfo,
    settings: defaultSettings as SettingProps,
  };
}

// ProLayout 支持的api https://procomponents.ant.design/components/layout
export const layout: RunTimeLayoutConfig = ({ initialState }) => {
  return {
    avatarProps: {
      title: <RKAvatar />,
      render: (_, avatarChildren) => {
        return <AvatarDropdown menu>{avatarChildren}</AvatarDropdown>;
      },
    },
    waterMarkProps: {
      content: initialState?.currentUser?.username,
    },
    actionsRender: () => {
      return [<Notification key="notification" />];
    },
    footerRender: () => <Footer />,
    onPageChange: () => {
      const { location } = history;
      // 如果获取用户失败，提示重新登录
      if (!initialState?.currentUser && location.pathname !== loginPath) {
        message.error('获取用户信息失败，请重新登录！');
      }
    },

    links: isDev
      ? [
          <Link key="openapi" to="/umi/plugin/openapi" target="_blank">
            <LinkOutlined />
            <span>OpenAPI 文档</span>
          </Link>,
        ]
      : [],
    unAccessible: <UnAccessible />,
    onMenuHeaderClick: (e) => {
      if (!isMicroservice) {
        history.push('/');
        return;
      }
      // @ts-ignore
      if (e.target.nodeName === 'IMG') {
        window.open('/', '_self');
        return;
      }
      history.push('/');
    },
    ...initialState?.settings,
  };
};

/**
 * @name request 配置，可以配置错误处理
 * 它基于 axios 和 ahooks 的 useRequest 提供了一套统一的网络请求和错误处理方案。
 * @doc https://umijs.org/docs/max/request#配置
 */
export const request = {
  baseURL: defaultSettings.BASE_URL,
  ...errorConfig,
};
