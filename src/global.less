:root {
  --primary-color: #13c2c2; // 修改主题色
  --primary-hover-color: #36cfc9; // 修改主题色
  --cyan-5: #36cfc9;
}
html,
body,
#root {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l,
    'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
    'Noto Color Emoji';
}

.colorWeak {
  filter: invert(80%);
}

canvas {
  display: block;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

ul,
ol {
  list-style: none;
}

/*滚动条样式*/
::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 14px; /*高宽分别对应横竖滚动条的尺寸*/
  height: 14px;
}
::-webkit-scrollbar-thumb {
  border: 4px solid rgba(0, 0, 0, 0);
  /*滚动条里面小方块*/
  border-radius: 14px;
  // border-bottom: 4px solid rgba(0, 0, 0, 0);
  box-shadow: 6px 6px #d4d9e2 inset;
}
::-webkit-scrollbar-thumb:hover {
  background: #9ea5b2;
  box-shadow: 6px 6px #9ea5b2 inset;
}
::-webkit-scrollbar-track {
  background-color: transparent;
  /*滚动条里面轨道*/
  border-radius: 0;
}

// 在使用浏览器保存的数据时 输入框的样式
input:-webkit-autofill,
textarea:-webkit-autofill,
select:-webkit-autofill {
  // 背景颜色
  background-color: transparent !important;
  // 背景图片
  background-image: none !important;
  //设置input输入框的背景颜色为透明色
  -webkit-box-shadow: 0 0 0px 1000px transparent inset !important;
  transition: background-color 50000s ease-in-out 0s;
}

// 公共样式

.inner-table-link {
  height: auto;
  padding: 0;
  line-height: 16px;
}
.rk-none {
  display: none;
}

.inner-table {
  .ant-pro-card .ant-pro-card-body {
    padding-inline: 0;
  }
}

// 主题重置

a {
  color: var(--ant-primary-color);
  // 非禁用状态下的链接悬停颜色
  &:not([disabled]):not(.ant-btn-disabled):hover {
    color: var(--ant-primary-color-hover);
  }
}
.rk-a-span {
  span {
    color: var(--primary-color);
  }
}
.rk-none {
  display: none;
}
