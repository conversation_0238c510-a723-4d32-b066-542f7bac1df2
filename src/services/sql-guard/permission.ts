// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 查询“角色”内的权限 GET /api/v1/sqlaudit/permission/getPermissionByRoleId/${param0} */
export async function getPermissionByRoleId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getPermissionByRoleIdParams,
  options?: { [key: string]: any },
) {
  const { roleId: param0, ...queryParams } = params;
  return request<API.ResultListSubsysPermissionResponse>(
    `/api/v1/sqlaudit/permission/getPermissionByRoleId/${param0}`,
    {
      method: 'GET',
      params: { ...queryParams },
      ...(options || {}),
    },
  );
}

/** 分页查询权限 GET /api/v1/sqlaudit/permission/listPermission */
export async function listPermission(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.listPermissionParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultIPageSubsysPermissionResponse>(
    '/api/v1/sqlaudit/permission/listPermission',
    {
      method: 'GET',
      params: {
        // pageNum has a default value: 1
        pageNum: '1',
        // pageSize has a default value: 10
        pageSize: '10',
        ...params,
      },
      ...(options || {}),
    },
  );
}
