// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 给资源组分配审批流程 POST /api/v1/sqlaudit/approvalflow/assignApprovalFlowToResourceGroup */
export async function assignApprovalFlowToResourceGroup(
  body: API.ResourceGroupApprovalFlowRequest,
  options?: { [key: string]: any },
) {
  return request<API.ResultInteger>(
    '/api/v1/sqlaudit/approvalflow/assignApprovalFlowToResourceGroup',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 根据资源组ID查询审批流程 GET /api/v1/sqlaudit/approvalflow/getApprovalFlowByResourceGroupId/${param0} */
export async function getApprovalFlowByResourceGroupId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getApprovalFlowByResourceGroupIdParams,
  options?: { [key: string]: any },
) {
  const { resourceGroupId: param0, ...queryParams } = params;
  return request<API.ResultListApprovalFlowResponse>(
    `/api/v1/sqlaudit/approvalflow/getApprovalFlowByResourceGroupId/${param0}`,
    {
      method: 'GET',
      params: { ...queryParams },
      ...(options || {}),
    },
  );
}
