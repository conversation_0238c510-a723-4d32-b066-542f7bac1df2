// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 审批工单 POST /api/v1/sqlaudit/workorder/approveWorkOrder */
export async function approveWorkOrder(
  body: API.WorkOrderApprovalRequest,
  options?: { [key: string]: any },
) {
  return request<API.ResultInteger>('/api/v1/sqlaudit/workorder/approveWorkOrder', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 创建工单 POST /api/v1/sqlaudit/workorder/createWorkOrder */
export async function createWorkOrder(
  body: API.WorkOrderAddRequest,
  options?: { [key: string]: any },
) {
  return request<API.ResultInteger>('/api/v1/sqlaudit/workorder/createWorkOrder', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 执行工单 POST /api/v1/sqlaudit/workorder/executeWorkOrder */
export async function executeWorkOrder(
  body: API.WorkOrderExecuteRequest,
  options?: { [key: string]: any },
) {
  return request<API.ResultInteger>('/api/v1/sqlaudit/workorder/executeWorkOrder', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询工单审批历史 GET /api/v1/sqlaudit/workorder/getApprovalHistoryByWorkOrderId/${param0} */
export async function getApprovalHistoryByWorkOrderId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getApprovalHistoryByWorkOrderIdParams,
  options?: { [key: string]: any },
) {
  const { workOrderId: param0, ...queryParams } = params;
  return request<API.ResultListWorkOrderApprovalRecordReponse>(
    `/api/v1/sqlaudit/workorder/getApprovalHistoryByWorkOrderId/${param0}`,
    {
      method: 'GET',
      params: { ...queryParams },
      ...(options || {}),
    },
  );
}

/** 查询工单详情 GET /api/v1/sqlaudit/workorder/getWorkOrderDetail/${param0} */
export async function getWorkOrderDetail(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getWorkOrderDetailParams,
  options?: { [key: string]: any },
) {
  const { workOrderId: param0, ...queryParams } = params;
  return request<API.ResultWorkOrderDetailResponse>(
    `/api/v1/sqlaudit/workorder/getWorkOrderDetail/${param0}`,
    {
      method: 'GET',
      params: { ...queryParams },
      ...(options || {}),
    },
  );
}

/** 分页查询工单列表 GET /api/v1/sqlaudit/workorder/listWorkOrderPage */
export async function listWorkOrderPage(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.listWorkOrderPageParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultIPageWorkOrderResponse>('/api/v1/sqlaudit/workorder/listWorkOrderPage', {
    method: 'GET',
    params: {
      // pageNum has a default value: 1
      pageNum: '1',
      // pageSize has a default value: 10
      pageSize: '10',
      ...params,
    },
    ...(options || {}),
  });
}

/** 中止工单 POST /api/v1/sqlaudit/workorder/terminateWorkOrder */
export async function terminateWorkOrder(
  body: API.WorkOrderTerminateRequest,
  options?: { [key: string]: any },
) {
  return request<API.ResultInteger>('/api/v1/sqlaudit/workorder/terminateWorkOrder', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
