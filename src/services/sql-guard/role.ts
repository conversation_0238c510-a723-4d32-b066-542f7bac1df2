// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 添加子系统角色 POST /api/v1/sqlaudit/role/addSubsysRole */
export async function addSubsysRole(body: API.RoleAddRequest, options?: { [key: string]: any }) {
  return request<API.ResultInteger>('/api/v1/sqlaudit/role/addSubsysRole', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 给角色分配权限 POST /api/v1/sqlaudit/role/assignPermissionToRoles */
export async function assignPermissionToRoles(
  body: API.RolesPermissionRequest,
  options?: { [key: string]: any },
) {
  return request<API.ResultInteger>('/api/v1/sqlaudit/role/assignPermissionToRoles', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 给'业务角色'分配用户 POST /api/v1/sqlaudit/role/assignUsersToRoles */
export async function assignUsersToRoles(
  body: API.RolesUsersRequest,
  options?: { [key: string]: any },
) {
  return request<API.ResultInteger>('/api/v1/sqlaudit/role/assignUsersToRoles', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除角色 DELETE /api/v1/sqlaudit/role/deleteSubsysRole/${param0} */
export async function deleteSubsysRole(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteSubsysRoleParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.ResultInteger>(`/api/v1/sqlaudit/role/deleteSubsysRole/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 角色详情 GET /api/v1/sqlaudit/role/detailSubsysRole/${param0} */
export async function detailSubsysRole(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.detailSubsysRoleParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.ResultRoleResponse>(`/api/v1/sqlaudit/role/detailSubsysRole/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 分页查询角色 GET /api/v1/sqlaudit/role/listRole */
export async function listRole(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.listRoleParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultIPageRoleResponse>('/api/v1/sqlaudit/role/listRole', {
    method: 'GET',
    params: {
      // pageNum has a default value: 1
      pageNum: '1',
      // pageSize has a default value: 10
      pageSize: '10',
      ...params,
    },
    ...(options || {}),
  });
}

/** 更新子系统角色 POST /api/v1/sqlaudit/role/updateSubsysRole */
export async function updateSubsysRole(
  body: API.RoleUpdateRequest,
  options?: { [key: string]: any },
) {
  return request<API.ResultInteger>('/api/v1/sqlaudit/role/updateSubsysRole', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
