// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 此处后端没有提供注释 POST /api/v1/sqlaudit/user/all */
export async function allUser(options?: { [key: string]: any }) {
  return request<API.ResultListUserVO>('/api/v1/sqlaudit/user/all', {
    method: 'POST',
    ...(options || {}),
  });
}

/** 用户信息 POST /api/v1/sqlaudit/user/current */
export async function currentUser(options?: { [key: string]: any }) {
  return request<API.ResultUserVO>('/api/v1/sqlaudit/user/current', {
    method: 'POST',
    ...(options || {}),
  });
}

/** 查询“资源组”内的用户 GET /api/v1/sqlaudit/user/listUserOfGroup/${param0} */
export async function listUserOfGroup(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.listUserOfGroupParams,
  options?: { [key: string]: any },
) {
  const { groupId: param0, ...queryParams } = params;
  return request<API.ResultListSqlAuditUserResponse>(
    `/api/v1/sqlaudit/user/listUserOfGroup/${param0}`,
    {
      method: 'GET',
      params: { ...queryParams },
      ...(options || {}),
    },
  );
}

/** 查询“角色”内的用户 GET /api/v1/sqlaudit/user/listUserOfRole/${param0} */
export async function listUserOfRole(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.listUserOfRoleParams,
  options?: { [key: string]: any },
) {
  const { roleId: param0, ...queryParams } = params;
  return request<API.ResultListSqlAuditUserResponse>(
    `/api/v1/sqlaudit/user/listUserOfRole/${param0}`,
    {
      method: 'GET',
      params: { ...queryParams },
      ...(options || {}),
    },
  );
}

/** 登录 POST /api/v1/sqlaudit/user/login */
export async function userLogin(body: API.LoginRequest, options?: { [key: string]: any }) {
  return request<API.ResultLoginVO>('/api/v1/sqlaudit/user/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
