// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 获取审核参数信息 GET /api/v1/sqlaudit/audit_parameter/getParameter */
export async function getParameter(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getParameterParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultAuditParameterResponse>(
    '/api/v1/sqlaudit/audit_parameter/getParameter',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}

/** 分页查询审核参数信息 GET /api/v1/sqlaudit/audit_parameter/listParameter */
export async function listParameter(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.listParameterParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultIPageAuditParameterResponse>(
    '/api/v1/sqlaudit/audit_parameter/listParameter',
    {
      method: 'GET',
      params: {
        // pageNum has a default value: 1
        pageNum: '1',
        // pageSize has a default value: 10
        pageSize: '10',
        ...params,
      },
      ...(options || {}),
    },
  );
}

/** 更新审核参数的值 PUT /api/v1/sqlaudit/audit_parameter/updateParameter */
export async function updateParameter(
  body: API.AuditParameterUpdateRequest,
  options?: { [key: string]: any },
) {
  return request<API.ResultInteger>('/api/v1/sqlaudit/audit_parameter/updateParameter', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
