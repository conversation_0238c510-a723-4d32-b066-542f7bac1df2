// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 此处后端没有提供注释 PUT /api/v1/sqlaudit/notification/my/all/read */
export async function myAllRead(options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/v1/sqlaudit/notification/my/all/read', {
    method: 'PUT',
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 GET /api/v1/sqlaudit/notification/my/new/notification */
export async function myAllSystemNewNotification(options?: { [key: string]: any }) {
  return request<API.ResultListSystemNotificationVO>(
    '/api/v1/sqlaudit/notification/my/new/notification',
    {
      method: 'GET',
      ...(options || {}),
    },
  );
}

/** 此处后端没有提供注释 POST /api/v1/sqlaudit/notification/my/page/notification */
export async function myPageSystemNotification(
  body: API.NotificationPageRequest,
  options?: { [key: string]: any },
) {
  return request<API.ResultPageVOSystemNotificationVO>(
    '/api/v1/sqlaudit/notification/my/page/notification',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 此处后端没有提供注释 POST /api/v1/sqlaudit/notification/my/read */
export async function myRead(body: API.BatchIdRequest, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/v1/sqlaudit/notification/my/read', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
