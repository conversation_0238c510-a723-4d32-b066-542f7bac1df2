// @ts-ignore
/* eslint-disable */
// API 更新时间：
// API 唯一标识：
import * as approvalflow from './approvalflow';
import * as auditParameter from './auditParameter';
import * as dashboard from './dashboard';
import * as datasource from './datasource';
import * as notification from './notification';
import * as permission from './permission';
import * as report from './report';
import * as resourcegroup from './resourcegroup';
import * as role from './role';
import * as rule from './rule';
import * as ruleTemplate from './ruleTemplate';
import * as sqlRecord from './sqlRecord';
import * as sqlaudit from './sqlaudit';
import * as statistics from './statistics';
import * as taskAudit from './taskAudit';
import * as user from './user';
import * as workorder from './workorder';
export default {
  ruleTemplate,
  rule,
  resourcegroup,
  notification,
  datasource,
  auditParameter,
  workorder,
  user,
  taskAudit,
  role,
  report,
  sqlaudit,
  approvalflow,
  statistics,
  sqlRecord,
  permission,
  dashboard,
};
