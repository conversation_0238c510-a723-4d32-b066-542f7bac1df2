// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 添加规则模板 POST /api/v1/sqlaudit/rule_template/addRuleTemplate */
export async function addRuleTemplate(
  body: API.RuleTemplateAddRequest,
  options?: { [key: string]: any },
) {
  return request<API.ResultLong>('/api/v1/sqlaudit/rule_template/addRuleTemplate', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除规则模板 DELETE /api/v1/sqlaudit/rule_template/deleteRuleTemplate/${param0} */
export async function deleteRuleTemplate(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteRuleTemplateParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.ResultInteger>(`/api/v1/sqlaudit/rule_template/deleteRuleTemplate/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 查看 “模板” 详情 GET /api/v1/sqlaudit/rule_template/detailRuleTemplate/${param0} */
export async function detailRuleTemplate(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.detailRuleTemplateParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.ResultRuleTemplateResponse>(
    `/api/v1/sqlaudit/rule_template/detailRuleTemplate/${param0}`,
    {
      method: 'GET',
      params: { ...queryParams },
      ...(options || {}),
    },
  );
}

/** 根据 “数据库类型” 获取规则列表 GET /api/v1/sqlaudit/rule_template/list_rule_by_dbtype */
export async function listRuleByDbType(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.listRuleByDbTypeParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultIPageRuleResponse>(
    '/api/v1/sqlaudit/rule_template/list_rule_by_dbtype',
    {
      method: 'GET',
      params: {
        // pageNum has a default value: 1
        pageNum: '1',
        // pageSize has a default value: 10
        pageSize: '10',
        ...params,
      },
      ...(options || {}),
    },
  );
}

/** 根据 “模板ID” 获取规则列表 GET /api/v1/sqlaudit/rule_template/list_rule_by_templateid */
export async function listRuleByTemplateId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.listRuleByTemplateIdParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultIPageRuleResponse>(
    '/api/v1/sqlaudit/rule_template/list_rule_by_templateid',
    {
      method: 'GET',
      params: {
        // pageNum has a default value: 1
        pageNum: '1',
        // pageSize has a default value: 10
        pageSize: '10',
        ...params,
      },
      ...(options || {}),
    },
  );
}

/** 根据 “模板ID” 获取规则 GET /api/v1/sqlaudit/rule_template/list_rule_template */
export async function listRuleTemplate(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.listRuleTemplateParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultIPageRuleTemplateResponse>(
    '/api/v1/sqlaudit/rule_template/list_rule_template',
    {
      method: 'GET',
      params: {
        // pageNum has a default value: 1
        pageNum: '1',
        // pageSize has a default value: 10
        pageSize: '10',
        ...params,
      },
      ...(options || {}),
    },
  );
}

/** 根据 “模板ID” 获取 “未使用” 规则列表 GET /api/v1/sqlaudit/rule_template/list_unused_rule_by_templateid */
export async function listUnusedRuleByTemplateId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.listUnusedRuleByTemplateIdParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultIPageRuleResponse>(
    '/api/v1/sqlaudit/rule_template/list_unused_rule_by_templateid',
    {
      method: 'GET',
      params: {
        // pageNum has a default value: 1
        pageNum: '1',
        // pageSize has a default value: 10
        pageSize: '10',
        ...params,
      },
      ...(options || {}),
    },
  );
}

/** 更新 “规则模板”  PUT /api/v1/sqlaudit/rule_template/updateRuleTemplate */
export async function updateRuleTemplate(
  body: API.RuleTemplateUpdateRequest,
  options?: { [key: string]: any },
) {
  return request<API.ResultInteger>('/api/v1/sqlaudit/rule_template/updateRuleTemplate', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
