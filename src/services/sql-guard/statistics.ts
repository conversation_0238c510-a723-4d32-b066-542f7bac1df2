// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** SQL审核明细统计 GET /api/v1/sqlaudit/statistics/auditResultStatistics */
export async function detailStatistics(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.detailStatisticsParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultIPageAuditRusultStatisticsResponse>(
    '/api/v1/sqlaudit/statistics/auditResultStatistics',
    {
      method: 'GET',
      params: {
        // pageNum has a default value: 1
        pageNum: '1',
        // pageSize has a default value: 10
        pageSize: '10',
        ...params,
      },
      ...(options || {}),
    },
  );
}

/** 此处后端没有提供注释 GET /api/v1/sqlaudit/statistics/auditResultStatistics/export */
export async function auditResultStatisticsExport(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.auditResultStatisticsExportParams,
  options?: { [key: string]: any },
) {
  return request<any>('/api/v1/sqlaudit/statistics/auditResultStatistics/export', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 审核结果统计明细 GET /api/v1/sqlaudit/statistics/auditResultStatisticsDetail */
export async function auditResultStatisticsDetail(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.auditResultStatisticsDetailParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultIPageAuditRusultStatisticsDetailResponse>(
    '/api/v1/sqlaudit/statistics/auditResultStatisticsDetail',
    {
      method: 'GET',
      params: {
        // pageNum has a default value: 1
        pageNum: '1',
        // pageSize has a default value: 10
        pageSize: '10',
        ...params,
      },
      ...(options || {}),
    },
  );
}

/** 此处后端没有提供注释 GET /api/v1/sqlaudit/statistics/report/download */
export async function downloadReport1(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.downloadReport1Params,
  options?: { [key: string]: any },
) {
  return request<string>('/api/v1/sqlaudit/statistics/report/download', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 GET /api/v1/sqlaudit/statistics/report/html */
export async function previewReport(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.previewReportParams,
  options?: { [key: string]: any },
) {
  return request<string>('/api/v1/sqlaudit/statistics/report/html', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 汇总统计 GET /api/v1/sqlaudit/statistics/summaryStatistics */
export async function summaryStatistics(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.summaryStatisticsParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultListSummaryStatisticsResponse>(
    '/api/v1/sqlaudit/statistics/summaryStatistics',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}

/** 此处后端没有提供注释 GET /api/v1/sqlaudit/statistics/summaryStatistics/export */
export async function exportUsingGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.exportUsingGETParams,
  options?: { [key: string]: any },
) {
  return request<any>('/api/v1/sqlaudit/statistics/summaryStatistics/export', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 违反规则统计 GET /api/v1/sqlaudit/statistics/violationsRuleStatistics */
export async function violationsRuleStatistics(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.violationsRuleStatisticsParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultIPageViolationsRuleStatisticsResponse>(
    '/api/v1/sqlaudit/statistics/violationsRuleStatistics',
    {
      method: 'GET',
      params: {
        // pageNum has a default value: 1
        pageNum: '1',
        // pageSize has a default value: 10
        pageSize: '10',
        ...params,
      },
      ...(options || {}),
    },
  );
}

/** 此处后端没有提供注释 GET /api/v1/sqlaudit/statistics/violationsRuleStatistics/export */
export async function violationsRuleStatisticsExport(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.violationsRuleStatisticsExportParams,
  options?: { [key: string]: any },
) {
  return request<any>('/api/v1/sqlaudit/statistics/violationsRuleStatistics/export', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 违反规则统计详情 GET /api/v1/sqlaudit/statistics/violationsRuleStatisticsDetail */
export async function violationsRuleStatisticsDetail(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.violationsRuleStatisticsDetailParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultIPageViolationsRuleStatisticsDetailResponse>(
    '/api/v1/sqlaudit/statistics/violationsRuleStatisticsDetail',
    {
      method: 'GET',
      params: {
        // pageNum has a default value: 1
        pageNum: '1',
        // pageSize has a default value: 10
        pageSize: '10',
        ...params,
      },
      ...(options || {}),
    },
  );
}
