// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 此处后端没有提供注释 POST /api/v1/sqlaudit/report/audit */
export async function auditReport(body: API.AuditReportRequest, options?: { [key: string]: any }) {
  return request<API.ResultIPageAuditReport>('/api/v1/sqlaudit/report/audit', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
