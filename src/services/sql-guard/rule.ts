// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 添加规则 POST /api/v1/sqlaudit/rule/addRule */
export async function addRule(body: API.RuleAddRequest, options?: { [key: string]: any }) {
  return request<API.ResultInteger>('/api/v1/sqlaudit/rule/addRule', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除规则 DELETE /api/v1/sqlaudit/rule/deleteRule/${param0} */
export async function deleteRule(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteRuleParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.ResultInteger>(`/api/v1/sqlaudit/rule/deleteRule/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 查看规则详情 GET /api/v1/sqlaudit/rule/detailRule/${param0} */
export async function detailRule(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.detailRuleParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.ResultRuleResponse>(`/api/v1/sqlaudit/rule/detailRule/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 根据 “规则ID” 查询 “基线定义”  GET /api/v1/sqlaudit/rule/getBaseLineDetailsListByRuleId */
export async function getBaseLineDetailsListByRuleId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getBaseLineDetailsListByRuleIdParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultListRuleBaseLineDetailResponse>(
    '/api/v1/sqlaudit/rule/getBaseLineDetailsListByRuleId',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}

/** 根据 “规则ID” 和 “模板ID” 查询基线详情 GET /api/v1/sqlaudit/rule/getBaseLineDetailsListByRuleIdAndTemplateId */
export async function getBaseLineDetailsListByRuleIdAndTemplateId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getBaseLineDetailsListByRuleIdAndTemplateIdParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultListRuleBaseLineDetailResponse>(
    '/api/v1/sqlaudit/rule/getBaseLineDetailsListByRuleIdAndTemplateId',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}

/** 分页查询规则 GET /api/v1/sqlaudit/rule/listRules */
export async function listRules(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.listRulesParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultIPageRuleResponse>('/api/v1/sqlaudit/rule/listRules', {
    method: 'GET',
    params: {
      // pageNum has a default value: 1
      pageNum: '1',
      // pageSize has a default value: 10
      pageSize: '10',
      ...params,
    },
    ...(options || {}),
  });
}

/** 根据基线详情ID更新基线标准点（参数） PUT /api/v1/sqlaudit/rule/updateBaseLineByInfo */
export async function updateBaseLineByInfo(
  body: API.RuleBaseLineDetailUpdateRequest,
  options?: { [key: string]: any },
) {
  return request<API.ResultInteger>('/api/v1/sqlaudit/rule/updateBaseLineByInfo', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新非关键信息 PUT /api/v1/sqlaudit/rule/updateNonCritical */
export async function updateNonCritical(
  body: API.RuleUpdateNonCriticalRequest,
  options?: { [key: string]: any },
) {
  return request<API.ResultInteger>('/api/v1/sqlaudit/rule/updateNonCritical', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新规则 PUT /api/v1/sqlaudit/rule/updateRule */
export async function updateRule(body: API.RuleUpdateRequest, options?: { [key: string]: any }) {
  return request<API.ResultInteger>('/api/v1/sqlaudit/rule/updateRule', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
