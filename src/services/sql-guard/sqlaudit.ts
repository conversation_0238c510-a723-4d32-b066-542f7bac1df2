// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** sql审核接口 POST /api/v1/sqlaudit/audit/auditSQL */
export async function auditSql(body: API.SqlAuditRequest, options?: { [key: string]: any }) {
  return request<API.ResultListSqlAuditResponse>('/api/v1/sqlaudit/audit/auditSQL', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 违反审核规则的详情，根据审核结果ID查询审核结果详情 GET /api/v1/sqlaudit/audit/listViolationsByAuditResultId */
export async function listViolationsByAuditResultId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.listViolationsByAuditResultIdParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultIPageAuditResultViolationsResponse>(
    '/api/v1/sqlaudit/audit/listViolationsByAuditResultId',
    {
      method: 'GET',
      params: {
        // pageNum has a default value: 1
        pageNum: '1',
        // pageSize has a default value: 10
        pageSize: '10',
        ...params,
      },
      ...(options || {}),
    },
  );
}

/** 此处后端没有提供注释 POST /api/v1/sqlaudit/audit/quickAudit */
export async function quickAudit(body: API.QuickAuditRequest, options?: { [key: string]: any }) {
  return request<string>('/api/v1/sqlaudit/audit/quickAudit', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 POST /api/v1/sqlaudit/audit/report/download */
export async function downloadReport(
  body: API.QuickAuditRequest,
  options?: { [key: string]: any },
) {
  return request<string>('/api/v1/sqlaudit/audit/report/download', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** sql格式化 POST /api/v1/sqlaudit/audit/sqlFormat */
export async function sqlFormat(body: API.SqlFormat, options?: { [key: string]: any }) {
  return request<API.ResultSqlFormatDTO>('/api/v1/sqlaudit/audit/sqlFormat', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
