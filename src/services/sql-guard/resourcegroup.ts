// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 添加资源组 POST /api/v1/sqlaudit/resourcegroup/addResourceGroup */
export async function addResourceGroup(
  body: API.ResourceGroupAddRequest,
  options?: { [key: string]: any },
) {
  return request<API.ResultInteger>('/api/v1/sqlaudit/resourcegroup/addResourceGroup', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 给'资源组'分配用户 POST /api/v1/sqlaudit/resourcegroup/assignUsersToGroup */
export async function assignUsersToGroup(
  body: API.ResourceGroupUsersRequest,
  options?: { [key: string]: any },
) {
  return request<API.ResultInteger>('/api/v1/sqlaudit/resourcegroup/assignUsersToGroup', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除资源组 DELETE /api/v1/sqlaudit/resourcegroup/deleteResourceGroup/${param0} */
export async function deleteResourceGroup(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteResourceGroupParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.ResultInteger>(
    `/api/v1/sqlaudit/resourcegroup/deleteResourceGroup/${param0}`,
    {
      method: 'DELETE',
      params: { ...queryParams },
      ...(options || {}),
    },
  );
}

/** 查看资源组详情 GET /api/v1/sqlaudit/resourcegroup/detailResourceGroup/${param0} */
export async function detailResourceGroup(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.detailResourceGroupParams,
  options?: { [key: string]: any },
) {
  const { groupId: param0, ...queryParams } = params;
  return request<API.ResultResourceGroupResponse>(
    `/api/v1/sqlaudit/resourcegroup/detailResourceGroup/${param0}`,
    {
      method: 'GET',
      params: { ...queryParams },
      ...(options || {}),
    },
  );
}

/** 查询登陆者的授权组 GET /api/v1/sqlaudit/resourcegroup/listAuthGroup */
export async function listAuthGroup(options?: { [key: string]: any }) {
  return request<API.ResultListResourceGroupIdAndNameResponse>(
    '/api/v1/sqlaudit/resourcegroup/listAuthGroup',
    {
      method: 'GET',
      ...(options || {}),
    },
  );
}

/** 获取所有的资源组的id和name GET /api/v1/sqlaudit/resourcegroup/listIdAndName */
export async function listIdAndName(options?: { [key: string]: any }) {
  return request<API.ResultIPageResourceGroupIdAndNameResponse>(
    '/api/v1/sqlaudit/resourcegroup/listIdAndName',
    {
      method: 'GET',
      ...(options || {}),
    },
  );
}

/** 分页查询资源组 GET /api/v1/sqlaudit/resourcegroup/listResourceGroup */
export async function listResourceGroup(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.listResourceGroupParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultIPageResourceGroupResponse>(
    '/api/v1/sqlaudit/resourcegroup/listResourceGroup',
    {
      method: 'GET',
      params: {
        // pageNum has a default value: 1
        pageNum: '1',
        // pageSize has a default value: 10
        pageSize: '10',
        ...params,
      },
      ...(options || {}),
    },
  );
}

/** 更新资源组信息 PUT /api/v1/sqlaudit/resourcegroup/updateResourceGroup */
export async function updateResourceGroup(
  body: API.ResourceGroupUpdateRequest,
  options?: { [key: string]: any },
) {
  return request<API.ResultInteger>('/api/v1/sqlaudit/resourcegroup/updateResourceGroup', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
