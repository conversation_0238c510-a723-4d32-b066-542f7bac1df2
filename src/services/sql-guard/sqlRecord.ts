// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 分页查询 SQL 记录 GET /api/v1/sqlaudit/sql-record/listSqlDetail */
export async function listSqlDetail(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.listSqlDetailParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultIPageOriginalSqlDetailResponse>(
    '/api/v1/sqlaudit/sql-record/listSqlDetail',
    {
      method: 'GET',
      params: {
        // pageNo has a default value: 1
        pageNo: '1',
        // pageSize has a default value: 10
        pageSize: '10',
        ...params,
      },
      ...(options || {}),
    },
  );
}

/** 分页查询 SQL 记录 GET /api/v1/sqlaudit/sql-record/listSqlRecords */
export async function listSqlRecords(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.listSqlRecordsParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultIPageOriginalSqlResponse>('/api/v1/sqlaudit/sql-record/listSqlRecords', {
    method: 'GET',
    params: {
      // pageNo has a default value: 1
      pageNo: '1',
      // pageSize has a default value: 10
      pageSize: '10',
      ...params,
    },
    ...(options || {}),
  });
}
