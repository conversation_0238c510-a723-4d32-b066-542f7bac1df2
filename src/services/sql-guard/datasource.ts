// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 添加数据源 POST /api/v1/sqlaudit/datasource/addDataSource */
export async function addDataSource(
  body: API.DataSourceAddRequest,
  options?: { [key: string]: any },
) {
  return request<API.ResultInteger>('/api/v1/sqlaudit/datasource/addDataSource', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除数据源 DELETE /api/v1/sqlaudit/datasource/deleteDataSource/${param0} */
export async function deleteDataSource(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteDataSourceParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.ResultInteger>(`/api/v1/sqlaudit/datasource/deleteDataSource/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 查看数据源详情 GET /api/v1/sqlaudit/datasource/detailDataSource/${param0} */
export async function detailDataSource(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.detailDataSourceParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.ResultDataSourceResponse>(
    `/api/v1/sqlaudit/datasource/detailDataSource/${param0}`,
    {
      method: 'GET',
      params: { ...queryParams },
      ...(options || {}),
    },
  );
}

/** 数据源列表 GET /api/v1/sqlaudit/datasource/listDataSource */
export async function listDataSource(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.listDataSourceParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultIPageDataSourceResponse>('/api/v1/sqlaudit/datasource/listDataSource', {
    method: 'GET',
    params: {
      // pageNum has a default value: 1
      pageNum: '1',
      // pageSize has a default value: 10
      pageSize: '10',
      ...params,
    },
    ...(options || {}),
  });
}

/** 根据 ‘数据源id’ 查询所有 ‘数据库名或schema’  GET /api/v1/sqlaudit/datasource/listDbNameOrSchemaBysourceId */
export async function listDbNameOrSchemaBysourceId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.listDbNameOrSchemaBysourceIdParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultListString>('/api/v1/sqlaudit/datasource/listDbNameOrSchemaBysourceId', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查询资源组下的 “（动态或静态）数据源” ，（必传auditMode-审核模式）  GET /api/v1/sqlaudit/datasource/listDSByGroupIdAndAuditMode */
export async function listDsByGroupIdAndAuditMode(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.listDSByGroupIdAndAuditModeParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultListDataSourceGroupInfoResponse>(
    '/api/v1/sqlaudit/datasource/listDSByGroupIdAndAuditMode',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}

/** 根据数据源ID测试连接 GET /api/v1/sqlaudit/datasource/testConnectionById/id/${param0} */
export async function testConnectionById(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.testConnectionByIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.ResultString>(`/api/v1/sqlaudit/datasource/testConnectionById/id/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 根据连接信息测试连接 POST /api/v1/sqlaudit/datasource/testConnectionByInfo/info */
export async function testConnectionByInfo(
  body: API.DataSourceTestRequest,
  options?: { [key: string]: any },
) {
  return request<API.ResultString>('/api/v1/sqlaudit/datasource/testConnectionByInfo/info', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新数据源 PUT /api/v1/sqlaudit/datasource/updateDataSource */
export async function updateDataSource(
  body: API.DataSourceUpdateRequest,
  options?: { [key: string]: any },
) {
  return request<API.ResultInteger>('/api/v1/sqlaudit/datasource/updateDataSource', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
