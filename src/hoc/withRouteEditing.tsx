import React from 'react';
import { useParams } from 'react-router-dom';

export interface WithRouteEditingProps {
  isEditPage: boolean;
  id: string;
  dbType?: number;
}

const withRouteEditing = <P extends WithRouteEditingProps>(Component: React.ComponentType<P>) => {
  return (props: Omit<P, keyof WithRouteEditingProps>) => {
    const { id, dbType } = useParams();
    const isEditPage = !!id;

    // Merge the original component props with the route editing props
    const mergedProps = {
      ...props,
      isEditPage,
      id,
      dbType,
    } as P;

    return <Component {...mergedProps} />;
  };
};

export default withRouteEditing;
